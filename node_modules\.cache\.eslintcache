[{"C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js": "2", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useAnalytics.js": "3", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\analytics.js": "4", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js": "5", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js": "6", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\AnalyticsDebugger.js": "7", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js": "8", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js": "9", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js": "10", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardSection.js": "11", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js": "12", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\config\\analytics.js": "13", "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCheckout.js": "14"}, {"size": 253, "mtime": 1750790409543, "results": "15", "hashOfConfig": "16"}, {"size": 3871, "mtime": 1751565361678, "results": "17", "hashOfConfig": "16"}, {"size": 8069, "mtime": 1751564462690, "results": "18", "hashOfConfig": "16"}, {"size": 8332, "mtime": 1751565228966, "results": "19", "hashOfConfig": "16"}, {"size": 17108, "mtime": 1751564528165, "results": "20", "hashOfConfig": "16"}, {"size": 28451, "mtime": 1751564653069, "results": "21", "hashOfConfig": "16"}, {"size": 8284, "mtime": 1751565326750, "results": "22", "hashOfConfig": "16"}, {"size": 3837, "mtime": 1750614946000, "results": "23", "hashOfConfig": "16"}, {"size": 2802, "mtime": 1750614946000, "results": "24", "hashOfConfig": "16"}, {"size": 7496, "mtime": 1750614946000, "results": "25", "hashOfConfig": "16"}, {"size": 7302, "mtime": 1751564707800, "results": "26", "hashOfConfig": "16"}, {"size": 6127, "mtime": 1751389760889, "results": "27", "hashOfConfig": "16"}, {"size": 6410, "mtime": 1751565137421, "results": "28", "hashOfConfig": "16"}, {"size": 13813, "mtime": 1751565044144, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "cwcvdy", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\App.js", ["72"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\hooks\\useAnalytics.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\analytics.js", ["73"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\LandingPage.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ZodiacPage.js", ["74"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\AnalyticsDebugger.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\ParticleBackground.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\SmokeAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaAnimation.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCardSection.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\services\\HoroscopeService.js", [], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\config\\analytics.js", ["75"], [], "C:\\Users\\<USER>\\Desktop\\Horoscope\\src\\components\\KuberaCheckout.js", [], [], {"ruleId": "76", "severity": 1, "message": "77", "line": 6, "column": 10, "nodeType": "78", "messageId": "79", "endLine": 6, "endColumn": 22}, {"ruleId": "76", "severity": 1, "message": "80", "line": 9, "column": 3, "nodeType": "78", "messageId": "79", "endLine": 9, "endColumn": 16}, {"ruleId": "81", "severity": 1, "message": "82", "line": 517, "column": 6, "nodeType": "83", "endLine": 517, "endColumn": 15, "suggestions": "84"}, {"ruleId": "85", "severity": 1, "message": "86", "line": 230, "column": 1, "nodeType": "87", "endLine": 248, "endColumn": 3}, "no-unused-vars", "'useAnalytics' is defined but never used.", "Identifier", "unusedVar", "'CUSTOM_EVENTS' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useCallback has a missing dependency: 'analytics'. Either include it or remove the dependency array.", "ArrayExpression", ["88"], "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", {"desc": "89", "fix": "90"}, "Update the dependencies array to be: [analytics, sign.id]", {"range": "91", "text": "92"}, [16549, 16558], "[analytics, sign.id]"]