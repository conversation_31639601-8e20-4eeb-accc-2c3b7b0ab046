{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/Desktop/Horoscope/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import axios from'axios';class HoroscopeService{constructor(){this.baseURL=process.env.REACT_APP_API_URL||'http://localhost:5000/api';this.cache=new Map();this.cacheExpiry=24*60*60*1000;// 24 hours in milliseconds\n}// Get cache-busting headers\ngetCacheBustingHeaders(){return{'Cache-Control':'no-cache, no-store, must-revalidate','Pragma':'no-cache','Expires':'0','X-Requested-With':'XMLHttpRequest','X-Cache-Buster':Date.now().toString()};}// Get cached horoscope if it exists and is not expired\ngetCachedHoroscope(signId){const today=new Date().toDateString();const cacheKey=\"\".concat(signId,\"_\").concat(today);if(this.cache.has(cacheKey)){const cached=this.cache.get(cacheKey);if(Date.now()-cached.timestamp<this.cacheExpiry){return cached.data;}else{this.cache.delete(cacheKey);}}return null;}// Cache horoscope data\ncacheHoroscope(signId,horoscope){const today=new Date().toDateString();const cacheKey=\"\".concat(signId,\"_\").concat(today);this.cache.set(cacheKey,{data:horoscope,timestamp:Date.now()});}// Set cached horoscope data (alias for cacheHoroscope)\nsetCachedHoroscope(signId,data){let expiryHours=arguments.length>2&&arguments[2]!==undefined?arguments[2]:24;this.cacheHoroscope(signId,data,expiryHours);}// Helper method to get sign in Sinhala\ngetSignSinhala(signId){const signMap={'aries':'මේෂ','taurus':'වෘෂභ','gemini':'මිථුන','cancer':'කටක','leo':'සිංහ','virgo':'කන්‍යා','libra':'තුලා','scorpio':'වෘශ්චික','sagittarius':'ධනු','capricorn':'මකර','aquarius':'කුම්භ','pisces':'මීන'};return signMap[signId]||signId;}// Get horoscope from backend API\nasync getHoroscopeFromAPI(signId){try{const response=await axios.get(\"\".concat(this.baseURL,\"/horoscope/\").concat(signId),{timeout:10000,// 10 second timeout\nheaders:this.getCacheBustingHeaders()});if(response.data&&response.data.success){return response.data.data;}else{throw new Error('Invalid response from API');}}catch(error){var _error$response,_error$response2,_error$response3;console.error('API error details:',{message:error.message,status:(_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status,statusText:(_error$response2=error.response)===null||_error$response2===void 0?void 0:_error$response2.statusText,data:(_error$response3=error.response)===null||_error$response3===void 0?void 0:_error$response3.data});throw error;}}// Get specific category from backend API\nasync getCategoryFromAPI(signId,category){try{const response=await axios.get(\"\".concat(this.baseURL,\"/horoscope/\").concat(signId,\"/\").concat(category),{timeout:10000,// 10 second timeout\nheaders:this.getCacheBustingHeaders()});if(response.data&&response.data.success){return response.data.data;}else{throw new Error('Invalid response from API');}}catch(error){var _error$response4,_error$response5,_error$response6;console.error('API error details:',{message:error.message,status:(_error$response4=error.response)===null||_error$response4===void 0?void 0:_error$response4.status,statusText:(_error$response5=error.response)===null||_error$response5===void 0?void 0:_error$response5.statusText,data:(_error$response6=error.response)===null||_error$response6===void 0?void 0:_error$response6.data});throw error;}}// Main method to get horoscope\nasync getHoroscope(signId){try{// Check cache first\nconst cached=this.getCachedHoroscope(signId);if(cached){return cached;}// Try to get from API\ntry{const apiData=await this.getHoroscopeFromAPI(signId);this.cacheHoroscope(signId,apiData);return apiData;}catch(apiError){console.warn('API failed, using fallback:',apiError.message);// Return fallback data\nconst signSinhala=this.getSignSinhala(signId);const fallbackData=this.generateFallbackHoroscope(signSinhala);return fallbackData;}}catch(error){console.error('Error getting horoscope:',error);throw error;}}// Fallback horoscope generation\ngenerateFallbackHoroscope(signSinhala){const fallbackHoroscopes={'මේෂ':{love:'අද දිනය ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ශක්තිමත් දිනයක් වනු ඇත.',career:'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කිරීමට හොඳ අවස්ථාවක්.',health:'ශාරීරික ශක්තිය සහ ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී.',finance:'මූල්‍ය කටයුතුවල නව ආයෝජන අවස්ථා සොයා ගැනීමට හොඳ කාලයකි.',general:'සාමාන්‍ය ජීවිතයේ ධනාත්මක ශක්තිය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'}// Add other signs as needed\n};const horoscope=fallbackHoroscopes[signSinhala]||{love:'ආදරය ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',career:'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා ලැබෙනු ඇත.',health:'සෞඛ්‍ය කටයුතුවල සැලකිලිමත් වන්න.',finance:'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න.',general:'සාමාන්‍ය ජීවිතයේ සමතුලිත ප්‍රවේශයක් අවශ්‍ය වේ.'};return _objectSpread({sign:signSinhala,date:new Date().toLocaleDateString('si-LK')},horoscope);}}const horoscopeService=new HoroscopeService();export default horoscopeService;", "map": {"version": 3, "names": ["axios", "HoroscopeService", "constructor", "baseURL", "process", "env", "REACT_APP_API_URL", "cache", "Map", "cacheExpiry", "getCacheBustingHeaders", "Date", "now", "toString", "getCachedHoroscope", "signId", "today", "toDateString", "cache<PERSON>ey", "concat", "has", "cached", "get", "timestamp", "data", "delete", "cacheHoroscope", "horoscope", "set", "setCachedHoroscope", "expiryHours", "arguments", "length", "undefined", "getSignSinhala", "signMap", "getHoroscopeFromAPI", "response", "timeout", "headers", "success", "Error", "error", "_error$response", "_error$response2", "_error$response3", "console", "message", "status", "statusText", "getCategoryFromAPI", "category", "_error$response4", "_error$response5", "_error$response6", "getHoroscope", "apiData", "apiError", "warn", "signSinhala", "fallbackD<PERSON>", "generateFallbackHoroscope", "fallbackHoroscopes", "love", "career", "health", "finance", "general", "_objectSpread", "sign", "date", "toLocaleDateString", "horoscopeService"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/services/HoroscopeService.js"], "sourcesContent": ["import axios from 'axios';\n\nclass HoroscopeService {\n  constructor() {\n    this.baseURL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';\n    this.cache = new Map();\n    this.cacheExpiry = 24 * 60 * 60 * 1000; // 24 hours in milliseconds\n  }\n\n  // Get cache-busting headers\n  getCacheBustingHeaders() {\n    return {\n      'Cache-Control': 'no-cache, no-store, must-revalidate',\n      'Pragma': 'no-cache',\n      'Expires': '0',\n      'X-Requested-With': 'XMLHttpRequest',\n      'X-Cache-Buster': Date.now().toString()\n    };\n  }\n\n  // Get cached horoscope if it exists and is not expired\n  getCachedHoroscope(signId) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    if (this.cache.has(cacheKey)) {\n      const cached = this.cache.get(cacheKey);\n      if (Date.now() - cached.timestamp < this.cacheExpiry) {\n        return cached.data;\n      } else {\n        this.cache.delete(cacheKey);\n      }\n    }\n    \n    return null;\n  }\n\n  // Cache horoscope data\n  cacheHoroscope(signId, horoscope) {\n    const today = new Date().toDateString();\n    const cacheKey = `${signId}_${today}`;\n    \n    this.cache.set(cacheKey, {\n      data: horoscope,\n      timestamp: Date.now()\n    });\n  }\n\n  // Set cached horoscope data (alias for cacheHoroscope)\n  setCachedHoroscope(signId, data, expiryHours = 24) {\n    this.cacheHoroscope(signId, data, expiryHours);\n  }\n\n  // Helper method to get sign in Sinhala\n  getSignSinhala(signId) {\n    const signMap = {\n      'aries': 'මේෂ',\n      'taurus': 'වෘෂභ',\n      'gemini': 'මිථුන',\n      'cancer': 'කටක',\n      'leo': 'සිංහ',\n      'virgo': 'කන්‍යා',\n      'libra': 'තුලා',\n      'scorpio': 'වෘශ්චික',\n      'sagittarius': 'ධනු',\n      'capricorn': 'මකර',\n      'aquarius': 'කුම්භ',\n      'pisces': 'මීන'\n    };\n    return signMap[signId] || signId;\n  }\n\n  // Get horoscope from backend API\n  async getHoroscopeFromAPI(signId) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}`, {\n        timeout: 10000, // 10 second timeout\n        headers: this.getCacheBustingHeaders()\n      });\n\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      console.error('API error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data\n      });\n      throw error;\n    }\n  }\n\n  // Get specific category from backend API\n  async getCategoryFromAPI(signId, category) {\n    try {\n      const response = await axios.get(`${this.baseURL}/horoscope/${signId}/${category}`, {\n        timeout: 10000, // 10 second timeout\n        headers: this.getCacheBustingHeaders()\n      });\n\n      if (response.data && response.data.success) {\n        return response.data.data;\n      } else {\n        throw new Error('Invalid response from API');\n      }\n    } catch (error) {\n      console.error('API error details:', {\n        message: error.message,\n        status: error.response?.status,\n        statusText: error.response?.statusText,\n        data: error.response?.data\n      });\n      throw error;\n    }\n  }\n\n  // Main method to get horoscope\n  async getHoroscope(signId) {\n    try {\n      // Check cache first\n      const cached = this.getCachedHoroscope(signId);\n      if (cached) {\n        return cached;\n      }\n\n      // Try to get from API\n      try {\n        const apiData = await this.getHoroscopeFromAPI(signId);\n        this.cacheHoroscope(signId, apiData);\n        return apiData;\n      } catch (apiError) {\n        console.warn('API failed, using fallback:', apiError.message);\n        // Return fallback data\n        const signSinhala = this.getSignSinhala(signId);\n        const fallbackData = this.generateFallbackHoroscope(signSinhala);\n        return fallbackData;\n      }\n    } catch (error) {\n      console.error('Error getting horoscope:', error);\n      throw error;\n    }\n  }\n\n  // Fallback horoscope generation\n  generateFallbackHoroscope(signSinhala) {\n    const fallbackHoroscopes = {\n      'මේෂ': {\n        love: 'අද දිනය ආදරය සහ සම්බන්ධතා ක්ෂේත්‍රයේ ශක්තිමත් දිනයක් වනු ඇත.',\n        career: 'වෘත්තීය ක්ෂේත්‍රයේ නායකත්ව ගුණාංග ප්‍රදර්ශනය කිරීමට හොඳ අවස්ථාවක්.',\n        health: 'ශාරීරික ශක්තිය සහ ක්‍රියාශීලිත්වය ඉහළ මට්ටමක පවතී.',\n        finance: 'මූල්‍ය කටයුතුවල නව ආයෝජන අවස්ථා සොයා ගැනීමට හොඳ කාලයකි.',\n        general: 'සාමාන්‍ය ජීවිතයේ ධනාත්මක ශක්තිය සහ සාර්ථකත්වය අපේක්ෂා කරන්න.'\n      }\n      // Add other signs as needed\n    };\n\n    const horoscope = fallbackHoroscopes[signSinhala] || {\n      love: 'ආදරය ක්ෂේත්‍රයේ ධනාත්මක වෙනස්කම් අපේක්ෂා කරන්න.',\n      career: 'වෘත්තීය ක්ෂේත්‍රයේ නව අවස්ථා ලැබෙනු ඇත.',\n      health: 'සෞඛ්‍ය කටයුතුවල සැලකිලිමත් වන්න.',\n      finance: 'මූල්‍ය කටයුතුවල ප්‍රවේශම්කාරී වන්න.',\n      general: 'සාමාන්‍ය ජීවිතයේ සමතුලිත ප්‍රවේශයක් අවශ්‍ය වේ.'\n    };\n\n    return {\n      sign: signSinhala,\n      date: new Date().toLocaleDateString('si-LK'),\n      ...horoscope\n    };\n  }\n}\n\nconst horoscopeService = new HoroscopeService();\nexport default horoscopeService;"], "mappings": "qHAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,gBAAiB,CACrBC,WAAWA,CAAA,CAAG,CACZ,IAAI,CAACC,OAAO,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAC3E,IAAI,CAACC,KAAK,CAAG,GAAI,CAAAC,GAAG,CAAC,CAAC,CACtB,IAAI,CAACC,WAAW,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAE;AAC1C,CAEA;AACAC,sBAAsBA,CAAA,CAAG,CACvB,MAAO,CACL,eAAe,CAAE,qCAAqC,CACtD,QAAQ,CAAE,UAAU,CACpB,SAAS,CAAE,GAAG,CACd,kBAAkB,CAAE,gBAAgB,CACpC,gBAAgB,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CACxC,CAAC,CACH,CAEA;AACAC,kBAAkBA,CAACC,MAAM,CAAE,CACzB,KAAM,CAAAC,KAAK,CAAG,GAAI,CAAAL,IAAI,CAAC,CAAC,CAACM,YAAY,CAAC,CAAC,CACvC,KAAM,CAAAC,QAAQ,IAAAC,MAAA,CAAMJ,MAAM,MAAAI,MAAA,CAAIH,KAAK,CAAE,CAErC,GAAI,IAAI,CAACT,KAAK,CAACa,GAAG,CAACF,QAAQ,CAAC,CAAE,CAC5B,KAAM,CAAAG,MAAM,CAAG,IAAI,CAACd,KAAK,CAACe,GAAG,CAACJ,QAAQ,CAAC,CACvC,GAAIP,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGS,MAAM,CAACE,SAAS,CAAG,IAAI,CAACd,WAAW,CAAE,CACpD,MAAO,CAAAY,MAAM,CAACG,IAAI,CACpB,CAAC,IAAM,CACL,IAAI,CAACjB,KAAK,CAACkB,MAAM,CAACP,QAAQ,CAAC,CAC7B,CACF,CAEA,MAAO,KAAI,CACb,CAEA;AACAQ,cAAcA,CAACX,MAAM,CAAEY,SAAS,CAAE,CAChC,KAAM,CAAAX,KAAK,CAAG,GAAI,CAAAL,IAAI,CAAC,CAAC,CAACM,YAAY,CAAC,CAAC,CACvC,KAAM,CAAAC,QAAQ,IAAAC,MAAA,CAAMJ,MAAM,MAAAI,MAAA,CAAIH,KAAK,CAAE,CAErC,IAAI,CAACT,KAAK,CAACqB,GAAG,CAACV,QAAQ,CAAE,CACvBM,IAAI,CAAEG,SAAS,CACfJ,SAAS,CAAEZ,IAAI,CAACC,GAAG,CAAC,CACtB,CAAC,CAAC,CACJ,CAEA;AACAiB,kBAAkBA,CAACd,MAAM,CAAES,IAAI,CAAoB,IAAlB,CAAAM,WAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,EAAE,CAC/C,IAAI,CAACL,cAAc,CAACX,MAAM,CAAES,IAAI,CAAEM,WAAW,CAAC,CAChD,CAEA;AACAI,cAAcA,CAACnB,MAAM,CAAE,CACrB,KAAM,CAAAoB,OAAO,CAAG,CACd,OAAO,CAAE,KAAK,CACd,QAAQ,CAAE,MAAM,CAChB,QAAQ,CAAE,OAAO,CACjB,QAAQ,CAAE,KAAK,CACf,KAAK,CAAE,MAAM,CACb,OAAO,CAAE,QAAQ,CACjB,OAAO,CAAE,MAAM,CACf,SAAS,CAAE,SAAS,CACpB,aAAa,CAAE,KAAK,CACpB,WAAW,CAAE,KAAK,CAClB,UAAU,CAAE,OAAO,CACnB,QAAQ,CAAE,KACZ,CAAC,CACD,MAAO,CAAAA,OAAO,CAACpB,MAAM,CAAC,EAAIA,MAAM,CAClC,CAEA;AACA,KAAM,CAAAqB,mBAAmBA,CAACrB,MAAM,CAAE,CAChC,GAAI,CACF,KAAM,CAAAsB,QAAQ,CAAG,KAAM,CAAArC,KAAK,CAACsB,GAAG,IAAAH,MAAA,CAAI,IAAI,CAAChB,OAAO,gBAAAgB,MAAA,CAAcJ,MAAM,EAAI,CACtEuB,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,IAAI,CAAC7B,sBAAsB,CAAC,CACvC,CAAC,CAAC,CAEF,GAAI2B,QAAQ,CAACb,IAAI,EAAIa,QAAQ,CAACb,IAAI,CAACgB,OAAO,CAAE,CAC1C,MAAO,CAAAH,QAAQ,CAACb,IAAI,CAACA,IAAI,CAC3B,CAAC,IAAM,CACL,KAAM,IAAI,CAAAiB,KAAK,CAAC,2BAA2B,CAAC,CAC9C,CACF,CAAE,MAAOC,KAAK,CAAE,KAAAC,eAAA,CAAAC,gBAAA,CAAAC,gBAAA,CACdC,OAAO,CAACJ,KAAK,CAAC,oBAAoB,CAAE,CAClCK,OAAO,CAAEL,KAAK,CAACK,OAAO,CACtBC,MAAM,EAAAL,eAAA,CAAED,KAAK,CAACL,QAAQ,UAAAM,eAAA,iBAAdA,eAAA,CAAgBK,MAAM,CAC9BC,UAAU,EAAAL,gBAAA,CAAEF,KAAK,CAACL,QAAQ,UAAAO,gBAAA,iBAAdA,gBAAA,CAAgBK,UAAU,CACtCzB,IAAI,EAAAqB,gBAAA,CAAEH,KAAK,CAACL,QAAQ,UAAAQ,gBAAA,iBAAdA,gBAAA,CAAgBrB,IACxB,CAAC,CAAC,CACF,KAAM,CAAAkB,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAQ,kBAAkBA,CAACnC,MAAM,CAAEoC,QAAQ,CAAE,CACzC,GAAI,CACF,KAAM,CAAAd,QAAQ,CAAG,KAAM,CAAArC,KAAK,CAACsB,GAAG,IAAAH,MAAA,CAAI,IAAI,CAAChB,OAAO,gBAAAgB,MAAA,CAAcJ,MAAM,MAAAI,MAAA,CAAIgC,QAAQ,EAAI,CAClFb,OAAO,CAAE,KAAK,CAAE;AAChBC,OAAO,CAAE,IAAI,CAAC7B,sBAAsB,CAAC,CACvC,CAAC,CAAC,CAEF,GAAI2B,QAAQ,CAACb,IAAI,EAAIa,QAAQ,CAACb,IAAI,CAACgB,OAAO,CAAE,CAC1C,MAAO,CAAAH,QAAQ,CAACb,IAAI,CAACA,IAAI,CAC3B,CAAC,IAAM,CACL,KAAM,IAAI,CAAAiB,KAAK,CAAC,2BAA2B,CAAC,CAC9C,CACF,CAAE,MAAOC,KAAK,CAAE,KAAAU,gBAAA,CAAAC,gBAAA,CAAAC,gBAAA,CACdR,OAAO,CAACJ,KAAK,CAAC,oBAAoB,CAAE,CAClCK,OAAO,CAAEL,KAAK,CAACK,OAAO,CACtBC,MAAM,EAAAI,gBAAA,CAAEV,KAAK,CAACL,QAAQ,UAAAe,gBAAA,iBAAdA,gBAAA,CAAgBJ,MAAM,CAC9BC,UAAU,EAAAI,gBAAA,CAAEX,KAAK,CAACL,QAAQ,UAAAgB,gBAAA,iBAAdA,gBAAA,CAAgBJ,UAAU,CACtCzB,IAAI,EAAA8B,gBAAA,CAAEZ,KAAK,CAACL,QAAQ,UAAAiB,gBAAA,iBAAdA,gBAAA,CAAgB9B,IACxB,CAAC,CAAC,CACF,KAAM,CAAAkB,KAAK,CACb,CACF,CAEA;AACA,KAAM,CAAAa,YAAYA,CAACxC,MAAM,CAAE,CACzB,GAAI,CACF;AACA,KAAM,CAAAM,MAAM,CAAG,IAAI,CAACP,kBAAkB,CAACC,MAAM,CAAC,CAC9C,GAAIM,MAAM,CAAE,CACV,MAAO,CAAAA,MAAM,CACf,CAEA;AACA,GAAI,CACF,KAAM,CAAAmC,OAAO,CAAG,KAAM,KAAI,CAACpB,mBAAmB,CAACrB,MAAM,CAAC,CACtD,IAAI,CAACW,cAAc,CAACX,MAAM,CAAEyC,OAAO,CAAC,CACpC,MAAO,CAAAA,OAAO,CAChB,CAAE,MAAOC,QAAQ,CAAE,CACjBX,OAAO,CAACY,IAAI,CAAC,6BAA6B,CAAED,QAAQ,CAACV,OAAO,CAAC,CAC7D;AACA,KAAM,CAAAY,WAAW,CAAG,IAAI,CAACzB,cAAc,CAACnB,MAAM,CAAC,CAC/C,KAAM,CAAA6C,YAAY,CAAG,IAAI,CAACC,yBAAyB,CAACF,WAAW,CAAC,CAChE,MAAO,CAAAC,YAAY,CACrB,CACF,CAAE,MAAOlB,KAAK,CAAE,CACdI,OAAO,CAACJ,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACAmB,yBAAyBA,CAACF,WAAW,CAAE,CACrC,KAAM,CAAAG,kBAAkB,CAAG,CACzB,KAAK,CAAE,CACLC,IAAI,CAAE,8DAA8D,CACpEC,MAAM,CAAE,oEAAoE,CAC5EC,MAAM,CAAE,oDAAoD,CAC5DC,OAAO,CAAE,yDAAyD,CAClEC,OAAO,CAAE,8DACX,CACA;AACF,CAAC,CAED,KAAM,CAAAxC,SAAS,CAAGmC,kBAAkB,CAACH,WAAW,CAAC,EAAI,CACnDI,IAAI,CAAE,iDAAiD,CACvDC,MAAM,CAAE,yCAAyC,CACjDC,MAAM,CAAE,kCAAkC,CAC1CC,OAAO,CAAE,qCAAqC,CAC9CC,OAAO,CAAE,gDACX,CAAC,CAED,OAAAC,aAAA,EACEC,IAAI,CAAEV,WAAW,CACjBW,IAAI,CAAE,GAAI,CAAA3D,IAAI,CAAC,CAAC,CAAC4D,kBAAkB,CAAC,OAAO,CAAC,EACzC5C,SAAS,EAEhB,CACF,CAEA,KAAM,CAAA6C,gBAAgB,CAAG,GAAI,CAAAvE,gBAAgB,CAAC,CAAC,CAC/C,cAAe,CAAAuE,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}