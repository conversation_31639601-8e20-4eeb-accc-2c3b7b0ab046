{"ast": null, "code": "/**\n * Google Analytics Configuration for Kubera Horoscope Website\n * \n * This file contains all the analytics configuration and event definitions\n * for the horoscope website. Customize these settings according to your needs.\n */// Analytics Configuration\nexport const ANALYTICS_CONFIG={// Google Analytics 4 Measurement ID\n// Replace with your actual GA4 Measurement ID (format: G-XXXXXXXXXX)\nmeasurementId:process.env.REACT_APP_GA_MEASUREMENT_ID||'G-XXXXXXXXXX',// Enable/disable analytics in development\nenableInDevelopment:false,// Enable/disable console logging for analytics events\nenableLogging:process.env.NODE_ENV==='development',// Default currency for ecommerce tracking\ndefaultCurrency:'LKR',// Site information\nsiteInfo:{name:'කුබේර දෙවියන්ගේ ආශීර්වාදය',language:'si',// Sinhala\ncategory:'astrology',type:'horoscope_website'}};// Event Categories\nexport const EVENT_CATEGORIES={PAGE_INTERACTION:'page_interaction',USER_ENGAGEMENT:'user_engagement',CONTENT_INTERACTION:'content_interaction',ECOMMERCE:'ecommerce',FORM_INTERACTION:'form_interaction',MEDIA_INTERACTION:'media_interaction',ERROR:'error',PERFORMANCE:'performance',USER_PREFERENCE:'user_preference',SPIRITUAL_ACTIVITY:'spiritual_activity'};// Custom Event Names\nexport const CUSTOM_EVENTS={// Page Events\nLANDING_PAGE_LOADED:'landing_page_loaded',ZODIAC_PAGE_VIEWED:'zodiac_page_viewed',HOROSCOPE_LOADED:'horoscope_loaded',HOROSCOPE_REFRESH:'horoscope_refresh',// Content Engagement\nCONTENT_CARD_VIEWED:'content_card_viewed',SCROLL_MILESTONE:'scroll_milestone',TIME_ON_PAGE:'time_on_page',// Zodiac Interactions\nZODIAC_SELECTION:'zodiac_selection',ZODIAC_CARD_CLICK:'zodiac_card_click',// Product/Ecommerce Events\nPRODUCT_VIEW:'view_item',PRODUCT_ADD_TO_CART:'add_to_cart',CHECKOUT_BEGIN:'begin_checkout',CHECKOUT_PROGRESS:'checkout_progress',PURCHASE:'purchase',// Form Events\nFORM_START:'form_start',FORM_SUBMIT:'form_submit',FORM_ERROR:'form_error',FIELD_FOCUS:'form_field_focus',FIELD_BLUR:'form_field_blur',// Media Events\nAUDIO_PLAY:'audio_play',AUDIO_PAUSE:'audio_pause',SOUND_TOGGLE:'sound_toggle',// Spiritual/Cultural Events\nMANTRA_RECITATION:'mantra_recitation',BLESSING_REQUEST:'blessing_request',PRAYER_INTERACTION:'prayer_interaction',// User Preferences\nLANGUAGE_CHANGE:'language_change',THEME_CHANGE:'theme_change',// Errors\nAPI_ERROR:'api_error',JAVASCRIPT_ERROR:'javascript_error',NETWORK_ERROR:'network_error'};// Product Information for Ecommerce Tracking\nexport const PRODUCTS={KUBERA_CARD:{item_id:'kubera_card',item_name:'කුබේර කාඩ්පත්',item_name_english:'Kubera Card',item_category:'spiritual_products',item_category2:'astrology_cards',item_brand:'Kubera Blessings',price:1299,// Default price in LKR\ncurrency:'LKR'}};// Zodiac Signs Mapping (Sinhala to English)\nexport const ZODIAC_MAPPING={'මේෂ':'aries','වෘෂභ':'taurus','මිථුන':'gemini','කටක':'cancer','සිංහ':'leo','කන්‍යා':'virgo','තුලා':'libra','වෘශ්චික':'scorpio','ධනු':'sagittarius','මකර':'capricorn','කුම්භ':'aquarius','මීන':'pisces'};// Custom Dimensions and Metrics\nexport const CUSTOM_DIMENSIONS={USER_ZODIAC_SIGN:'user_zodiac_sign',PREFERRED_LANGUAGE:'preferred_language',VISITOR_TYPE:'visitor_type',// new, returning\nSESSION_TYPE:'session_type',CONTENT_CATEGORY:'content_category',DEVICE_TYPE:'device_type',REFERRER_TYPE:'referrer_type'};// Enhanced Ecommerce Parameters\nexport const ECOMMERCE_PARAMS={CURRENCY:'currency',VALUE:'value',TRANSACTION_ID:'transaction_id',ITEMS:'items',COUPON:'coupon',SHIPPING:'shipping',TAX:'tax',PAYMENT_TYPE:'payment_type'};// Form Names for Tracking\nexport const FORM_NAMES={KUBERA_CHECKOUT:'kubera_checkout',CONTACT_FORM:'contact_form',NEWSLETTER_SIGNUP:'newsletter_signup',FEEDBACK_FORM:'feedback_form'};// Page Types\nexport const PAGE_TYPES={LANDING:'landing',ZODIAC:'zodiac',PRODUCT:'product',CHECKOUT:'checkout',CONFIRMATION:'confirmation',ERROR:'error'};// User Interaction Types\nexport const INTERACTION_TYPES={CLICK:'click',HOVER:'hover',SCROLL:'scroll',FOCUS:'focus',BLUR:'blur',SUBMIT:'submit',CHANGE:'change'};// Media Types\nexport const MEDIA_TYPES={BACKGROUND_MUSIC:'background_music',SOUND_EFFECT:'sound_effect',MANTRA_AUDIO:'mantra_audio',NOTIFICATION_SOUND:'notification_sound'};// Error Types\nexport const ERROR_TYPES={API_ERROR:'api_error',NETWORK_ERROR:'network_error',VALIDATION_ERROR:'validation_error',JAVASCRIPT_ERROR:'javascript_error',PAYMENT_ERROR:'payment_error',FORM_ERROR:'form_error'};// Performance Metrics\nexport const PERFORMANCE_METRICS={PAGE_LOAD_TIME:'page_load_time',API_RESPONSE_TIME:'api_response_time',IMAGE_LOAD_TIME:'image_load_time',AUDIO_LOAD_TIME:'audio_load_time',FIRST_CONTENTFUL_PAINT:'first_contentful_paint',LARGEST_CONTENTFUL_PAINT:'largest_contentful_paint'};// Scroll Depth Milestones\nexport const SCROLL_MILESTONES=[25,50,75,90,100];// Time Engagement Thresholds (in seconds)\nexport const ENGAGEMENT_THRESHOLDS={QUICK_VIEW:5,ENGAGED_VIEW:30,DEEP_ENGAGEMENT:120,VERY_ENGAGED:300};// Default User Properties\nexport const DEFAULT_USER_PROPERTIES={website_language:'sinhala',website_type:'astrology',content_category:'horoscope',site_version:'1.0'};// Analytics Debug Mode\nexport const DEBUG_MODE=process.env.NODE_ENV==='development';// Export all configurations\nexport default{ANALYTICS_CONFIG,EVENT_CATEGORIES,CUSTOM_EVENTS,PRODUCTS,ZODIAC_MAPPING,CUSTOM_DIMENSIONS,ECOMMERCE_PARAMS,FORM_NAMES,PAGE_TYPES,INTERACTION_TYPES,MEDIA_TYPES,ERROR_TYPES,PERFORMANCE_METRICS,SCROLL_MILESTONES,ENGAGEMENT_THRESHOLDS,DEFAULT_USER_PROPERTIES,DEBUG_MODE};", "map": {"version": 3, "names": ["ANALYTICS_CONFIG", "measurementId", "process", "env", "REACT_APP_GA_MEASUREMENT_ID", "enableInDevelopment", "enableLogging", "NODE_ENV", "defaultCurrency", "siteInfo", "name", "language", "category", "type", "EVENT_CATEGORIES", "PAGE_INTERACTION", "USER_ENGAGEMENT", "CONTENT_INTERACTION", "ECOMMERCE", "FORM_INTERACTION", "MEDIA_INTERACTION", "ERROR", "PERFORMANCE", "USER_PREFERENCE", "SPIRITUAL_ACTIVITY", "CUSTOM_EVENTS", "LANDING_PAGE_LOADED", "ZODIAC_PAGE_VIEWED", "HOROSCOPE_LOADED", "HOROSCOPE_REFRESH", "CONTENT_CARD_VIEWED", "SCROLL_MILESTONE", "TIME_ON_PAGE", "ZODIAC_SELECTION", "ZODIAC_CARD_CLICK", "PRODUCT_VIEW", "PRODUCT_ADD_TO_CART", "CHECKOUT_BEGIN", "CHECKOUT_PROGRESS", "PURCHASE", "FORM_START", "FORM_SUBMIT", "FORM_ERROR", "FIELD_FOCUS", "FIELD_BLUR", "AUDIO_PLAY", "AUDIO_PAUSE", "SOUND_TOGGLE", "MANTRA_RECITATION", "BLESSING_REQUEST", "PRAYER_INTERACTION", "LANGUAGE_CHANGE", "THEME_CHANGE", "API_ERROR", "JAVASCRIPT_ERROR", "NETWORK_ERROR", "PRODUCTS", "KUBERA_CARD", "item_id", "item_name", "item_name_english", "item_category", "item_category2", "item_brand", "price", "currency", "ZODIAC_MAPPING", "CUSTOM_DIMENSIONS", "USER_ZODIAC_SIGN", "PREFERRED_LANGUAGE", "VISITOR_TYPE", "SESSION_TYPE", "CONTENT_CATEGORY", "DEVICE_TYPE", "REFERRER_TYPE", "ECOMMERCE_PARAMS", "CURRENCY", "VALUE", "TRANSACTION_ID", "ITEMS", "COUPON", "SHIPPING", "TAX", "PAYMENT_TYPE", "FORM_NAMES", "KUBERA_CHECKOUT", "CONTACT_FORM", "NEWSLETTER_SIGNUP", "FEEDBACK_FORM", "PAGE_TYPES", "LANDING", "ZODIAC", "PRODUCT", "CHECKOUT", "CONFIRMATION", "INTERACTION_TYPES", "CLICK", "HOVER", "SCROLL", "FOCUS", "BLUR", "SUBMIT", "CHANGE", "MEDIA_TYPES", "BACKGROUND_MUSIC", "SOUND_EFFECT", "MANTRA_AUDIO", "NOTIFICATION_SOUND", "ERROR_TYPES", "VALIDATION_ERROR", "PAYMENT_ERROR", "PERFORMANCE_METRICS", "PAGE_LOAD_TIME", "API_RESPONSE_TIME", "IMAGE_LOAD_TIME", "AUDIO_LOAD_TIME", "FIRST_CONTENTFUL_PAINT", "LARGEST_CONTENTFUL_PAINT", "SCROLL_MILESTONES", "ENGAGEMENT_THRESHOLDS", "QUICK_VIEW", "ENGAGED_VIEW", "DEEP_ENGAGEMENT", "VERY_ENGAGED", "DEFAULT_USER_PROPERTIES", "website_language", "website_type", "content_category", "site_version", "DEBUG_MODE"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/config/analytics.js"], "sourcesContent": ["/**\n * Google Analytics Configuration for Kubera Horoscope Website\n * \n * This file contains all the analytics configuration and event definitions\n * for the horoscope website. Customize these settings according to your needs.\n */\n\n// Analytics Configuration\nexport const ANALYTICS_CONFIG = {\n  // Google Analytics 4 Measurement ID\n  // Replace with your actual GA4 Measurement ID (format: G-XXXXXXXXXX)\n  measurementId: process.env.REACT_APP_GA_MEASUREMENT_ID || 'G-XXXXXXXXXX',\n  \n  // Enable/disable analytics in development\n  enableInDevelopment: false,\n  \n  // Enable/disable console logging for analytics events\n  enableLogging: process.env.NODE_ENV === 'development',\n  \n  // Default currency for ecommerce tracking\n  defaultCurrency: 'LKR',\n  \n  // Site information\n  siteInfo: {\n    name: 'කුබේර දෙවියන්ගේ ආශීර්වාදය',\n    language: 'si', // Sinhala\n    category: 'astrology',\n    type: 'horoscope_website'\n  }\n};\n\n// Event Categories\nexport const EVENT_CATEGORIES = {\n  PAGE_INTERACTION: 'page_interaction',\n  USER_ENGAGEMENT: 'user_engagement',\n  CONTENT_INTERACTION: 'content_interaction',\n  ECOMMERCE: 'ecommerce',\n  FORM_INTERACTION: 'form_interaction',\n  MEDIA_INTERACTION: 'media_interaction',\n  ERROR: 'error',\n  PERFORMANCE: 'performance',\n  USER_PREFERENCE: 'user_preference',\n  SPIRITUAL_ACTIVITY: 'spiritual_activity'\n};\n\n// Custom Event Names\nexport const CUSTOM_EVENTS = {\n  // Page Events\n  LANDING_PAGE_LOADED: 'landing_page_loaded',\n  ZODIAC_PAGE_VIEWED: 'zodiac_page_viewed',\n  HOROSCOPE_LOADED: 'horoscope_loaded',\n  HOROSCOPE_REFRESH: 'horoscope_refresh',\n  \n  // Content Engagement\n  CONTENT_CARD_VIEWED: 'content_card_viewed',\n  SCROLL_MILESTONE: 'scroll_milestone',\n  TIME_ON_PAGE: 'time_on_page',\n  \n  // Zodiac Interactions\n  ZODIAC_SELECTION: 'zodiac_selection',\n  ZODIAC_CARD_CLICK: 'zodiac_card_click',\n  \n  // Product/Ecommerce Events\n  PRODUCT_VIEW: 'view_item',\n  PRODUCT_ADD_TO_CART: 'add_to_cart',\n  CHECKOUT_BEGIN: 'begin_checkout',\n  CHECKOUT_PROGRESS: 'checkout_progress',\n  PURCHASE: 'purchase',\n  \n  // Form Events\n  FORM_START: 'form_start',\n  FORM_SUBMIT: 'form_submit',\n  FORM_ERROR: 'form_error',\n  FIELD_FOCUS: 'form_field_focus',\n  FIELD_BLUR: 'form_field_blur',\n  \n  // Media Events\n  AUDIO_PLAY: 'audio_play',\n  AUDIO_PAUSE: 'audio_pause',\n  SOUND_TOGGLE: 'sound_toggle',\n  \n  // Spiritual/Cultural Events\n  MANTRA_RECITATION: 'mantra_recitation',\n  BLESSING_REQUEST: 'blessing_request',\n  PRAYER_INTERACTION: 'prayer_interaction',\n  \n  // User Preferences\n  LANGUAGE_CHANGE: 'language_change',\n  THEME_CHANGE: 'theme_change',\n  \n  // Errors\n  API_ERROR: 'api_error',\n  JAVASCRIPT_ERROR: 'javascript_error',\n  NETWORK_ERROR: 'network_error'\n};\n\n// Product Information for Ecommerce Tracking\nexport const PRODUCTS = {\n  KUBERA_CARD: {\n    item_id: 'kubera_card',\n    item_name: 'කුබේර කාඩ්පත්',\n    item_name_english: 'Kubera Card',\n    item_category: 'spiritual_products',\n    item_category2: 'astrology_cards',\n    item_brand: 'Kubera Blessings',\n    price: 1299, // Default price in LKR\n    currency: 'LKR'\n  }\n};\n\n// Zodiac Signs Mapping (Sinhala to English)\nexport const ZODIAC_MAPPING = {\n  'මේෂ': 'aries',\n  'වෘෂභ': 'taurus', \n  'මිථුන': 'gemini',\n  'කටක': 'cancer',\n  'සිංහ': 'leo',\n  'කන්‍යා': 'virgo',\n  'තුලා': 'libra',\n  'වෘශ්චික': 'scorpio',\n  'ධනු': 'sagittarius',\n  'මකර': 'capricorn',\n  'කුම්භ': 'aquarius',\n  'මීන': 'pisces'\n};\n\n// Custom Dimensions and Metrics\nexport const CUSTOM_DIMENSIONS = {\n  USER_ZODIAC_SIGN: 'user_zodiac_sign',\n  PREFERRED_LANGUAGE: 'preferred_language',\n  VISITOR_TYPE: 'visitor_type', // new, returning\n  SESSION_TYPE: 'session_type',\n  CONTENT_CATEGORY: 'content_category',\n  DEVICE_TYPE: 'device_type',\n  REFERRER_TYPE: 'referrer_type'\n};\n\n// Enhanced Ecommerce Parameters\nexport const ECOMMERCE_PARAMS = {\n  CURRENCY: 'currency',\n  VALUE: 'value',\n  TRANSACTION_ID: 'transaction_id',\n  ITEMS: 'items',\n  COUPON: 'coupon',\n  SHIPPING: 'shipping',\n  TAX: 'tax',\n  PAYMENT_TYPE: 'payment_type'\n};\n\n// Form Names for Tracking\nexport const FORM_NAMES = {\n  KUBERA_CHECKOUT: 'kubera_checkout',\n  CONTACT_FORM: 'contact_form',\n  NEWSLETTER_SIGNUP: 'newsletter_signup',\n  FEEDBACK_FORM: 'feedback_form'\n};\n\n// Page Types\nexport const PAGE_TYPES = {\n  LANDING: 'landing',\n  ZODIAC: 'zodiac',\n  PRODUCT: 'product',\n  CHECKOUT: 'checkout',\n  CONFIRMATION: 'confirmation',\n  ERROR: 'error'\n};\n\n// User Interaction Types\nexport const INTERACTION_TYPES = {\n  CLICK: 'click',\n  HOVER: 'hover',\n  SCROLL: 'scroll',\n  FOCUS: 'focus',\n  BLUR: 'blur',\n  SUBMIT: 'submit',\n  CHANGE: 'change'\n};\n\n// Media Types\nexport const MEDIA_TYPES = {\n  BACKGROUND_MUSIC: 'background_music',\n  SOUND_EFFECT: 'sound_effect',\n  MANTRA_AUDIO: 'mantra_audio',\n  NOTIFICATION_SOUND: 'notification_sound'\n};\n\n// Error Types\nexport const ERROR_TYPES = {\n  API_ERROR: 'api_error',\n  NETWORK_ERROR: 'network_error',\n  VALIDATION_ERROR: 'validation_error',\n  JAVASCRIPT_ERROR: 'javascript_error',\n  PAYMENT_ERROR: 'payment_error',\n  FORM_ERROR: 'form_error'\n};\n\n// Performance Metrics\nexport const PERFORMANCE_METRICS = {\n  PAGE_LOAD_TIME: 'page_load_time',\n  API_RESPONSE_TIME: 'api_response_time',\n  IMAGE_LOAD_TIME: 'image_load_time',\n  AUDIO_LOAD_TIME: 'audio_load_time',\n  FIRST_CONTENTFUL_PAINT: 'first_contentful_paint',\n  LARGEST_CONTENTFUL_PAINT: 'largest_contentful_paint'\n};\n\n// Scroll Depth Milestones\nexport const SCROLL_MILESTONES = [25, 50, 75, 90, 100];\n\n// Time Engagement Thresholds (in seconds)\nexport const ENGAGEMENT_THRESHOLDS = {\n  QUICK_VIEW: 5,\n  ENGAGED_VIEW: 30,\n  DEEP_ENGAGEMENT: 120,\n  VERY_ENGAGED: 300\n};\n\n// Default User Properties\nexport const DEFAULT_USER_PROPERTIES = {\n  website_language: 'sinhala',\n  website_type: 'astrology',\n  content_category: 'horoscope',\n  site_version: '1.0'\n};\n\n// Analytics Debug Mode\nexport const DEBUG_MODE = process.env.NODE_ENV === 'development';\n\n// Export all configurations\nexport default {\n  ANALYTICS_CONFIG,\n  EVENT_CATEGORIES,\n  CUSTOM_EVENTS,\n  PRODUCTS,\n  ZODIAC_MAPPING,\n  CUSTOM_DIMENSIONS,\n  ECOMMERCE_PARAMS,\n  FORM_NAMES,\n  PAGE_TYPES,\n  INTERACTION_TYPES,\n  MEDIA_TYPES,\n  ERROR_TYPES,\n  PERFORMANCE_METRICS,\n  SCROLL_MILESTONES,\n  ENGAGEMENT_THRESHOLDS,\n  DEFAULT_USER_PROPERTIES,\n  DEBUG_MODE\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA,GAEA;AACA,MAAO,MAAM,CAAAA,gBAAgB,CAAG,CAC9B;AACA;AACAC,aAAa,CAAEC,OAAO,CAACC,GAAG,CAACC,2BAA2B,EAAI,cAAc,CAExE;AACAC,mBAAmB,CAAE,KAAK,CAE1B;AACAC,aAAa,CAAEJ,OAAO,CAACC,GAAG,CAACI,QAAQ,GAAK,aAAa,CAErD;AACAC,eAAe,CAAE,KAAK,CAEtB;AACAC,QAAQ,CAAE,CACRC,IAAI,CAAE,2BAA2B,CACjCC,QAAQ,CAAE,IAAI,CAAE;AAChBC,QAAQ,CAAE,WAAW,CACrBC,IAAI,CAAE,mBACR,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,gBAAgB,CAAG,CAC9BC,gBAAgB,CAAE,kBAAkB,CACpCC,eAAe,CAAE,iBAAiB,CAClCC,mBAAmB,CAAE,qBAAqB,CAC1CC,SAAS,CAAE,WAAW,CACtBC,gBAAgB,CAAE,kBAAkB,CACpCC,iBAAiB,CAAE,mBAAmB,CACtCC,KAAK,CAAE,OAAO,CACdC,WAAW,CAAE,aAAa,CAC1BC,eAAe,CAAE,iBAAiB,CAClCC,kBAAkB,CAAE,oBACtB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,aAAa,CAAG,CAC3B;AACAC,mBAAmB,CAAE,qBAAqB,CAC1CC,kBAAkB,CAAE,oBAAoB,CACxCC,gBAAgB,CAAE,kBAAkB,CACpCC,iBAAiB,CAAE,mBAAmB,CAEtC;AACAC,mBAAmB,CAAE,qBAAqB,CAC1CC,gBAAgB,CAAE,kBAAkB,CACpCC,YAAY,CAAE,cAAc,CAE5B;AACAC,gBAAgB,CAAE,kBAAkB,CACpCC,iBAAiB,CAAE,mBAAmB,CAEtC;AACAC,YAAY,CAAE,WAAW,CACzBC,mBAAmB,CAAE,aAAa,CAClCC,cAAc,CAAE,gBAAgB,CAChCC,iBAAiB,CAAE,mBAAmB,CACtCC,QAAQ,CAAE,UAAU,CAEpB;AACAC,UAAU,CAAE,YAAY,CACxBC,WAAW,CAAE,aAAa,CAC1BC,UAAU,CAAE,YAAY,CACxBC,WAAW,CAAE,kBAAkB,CAC/BC,UAAU,CAAE,iBAAiB,CAE7B;AACAC,UAAU,CAAE,YAAY,CACxBC,WAAW,CAAE,aAAa,CAC1BC,YAAY,CAAE,cAAc,CAE5B;AACAC,iBAAiB,CAAE,mBAAmB,CACtCC,gBAAgB,CAAE,kBAAkB,CACpCC,kBAAkB,CAAE,oBAAoB,CAExC;AACAC,eAAe,CAAE,iBAAiB,CAClCC,YAAY,CAAE,cAAc,CAE5B;AACAC,SAAS,CAAE,WAAW,CACtBC,gBAAgB,CAAE,kBAAkB,CACpCC,aAAa,CAAE,eACjB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,QAAQ,CAAG,CACtBC,WAAW,CAAE,CACXC,OAAO,CAAE,aAAa,CACtBC,SAAS,CAAE,eAAe,CAC1BC,iBAAiB,CAAE,aAAa,CAChCC,aAAa,CAAE,oBAAoB,CACnCC,cAAc,CAAE,iBAAiB,CACjCC,UAAU,CAAE,kBAAkB,CAC9BC,KAAK,CAAE,IAAI,CAAE;AACbC,QAAQ,CAAE,KACZ,CACF,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,cAAc,CAAG,CAC5B,KAAK,CAAE,OAAO,CACd,MAAM,CAAE,QAAQ,CAChB,OAAO,CAAE,QAAQ,CACjB,KAAK,CAAE,QAAQ,CACf,MAAM,CAAE,KAAK,CACb,QAAQ,CAAE,OAAO,CACjB,MAAM,CAAE,OAAO,CACf,SAAS,CAAE,SAAS,CACpB,KAAK,CAAE,aAAa,CACpB,KAAK,CAAE,WAAW,CAClB,OAAO,CAAE,UAAU,CACnB,KAAK,CAAE,QACT,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,iBAAiB,CAAG,CAC/BC,gBAAgB,CAAE,kBAAkB,CACpCC,kBAAkB,CAAE,oBAAoB,CACxCC,YAAY,CAAE,cAAc,CAAE;AAC9BC,YAAY,CAAE,cAAc,CAC5BC,gBAAgB,CAAE,kBAAkB,CACpCC,WAAW,CAAE,aAAa,CAC1BC,aAAa,CAAE,eACjB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,gBAAgB,CAAG,CAC9BC,QAAQ,CAAE,UAAU,CACpBC,KAAK,CAAE,OAAO,CACdC,cAAc,CAAE,gBAAgB,CAChCC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,QAAQ,CAChBC,QAAQ,CAAE,UAAU,CACpBC,GAAG,CAAE,KAAK,CACVC,YAAY,CAAE,cAChB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,UAAU,CAAG,CACxBC,eAAe,CAAE,iBAAiB,CAClCC,YAAY,CAAE,cAAc,CAC5BC,iBAAiB,CAAE,mBAAmB,CACtCC,aAAa,CAAE,eACjB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,UAAU,CAAG,CACxBC,OAAO,CAAE,SAAS,CAClBC,MAAM,CAAE,QAAQ,CAChBC,OAAO,CAAE,SAAS,CAClBC,QAAQ,CAAE,UAAU,CACpBC,YAAY,CAAE,cAAc,CAC5BzE,KAAK,CAAE,OACT,CAAC,CAED;AACA,MAAO,MAAM,CAAA0E,iBAAiB,CAAG,CAC/BC,KAAK,CAAE,OAAO,CACdC,KAAK,CAAE,OAAO,CACdC,MAAM,CAAE,QAAQ,CAChBC,KAAK,CAAE,OAAO,CACdC,IAAI,CAAE,MAAM,CACZC,MAAM,CAAE,QAAQ,CAChBC,MAAM,CAAE,QACV,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,WAAW,CAAG,CACzBC,gBAAgB,CAAE,kBAAkB,CACpCC,YAAY,CAAE,cAAc,CAC5BC,YAAY,CAAE,cAAc,CAC5BC,kBAAkB,CAAE,oBACtB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,WAAW,CAAG,CACzBvD,SAAS,CAAE,WAAW,CACtBE,aAAa,CAAE,eAAe,CAC9BsD,gBAAgB,CAAE,kBAAkB,CACpCvD,gBAAgB,CAAE,kBAAkB,CACpCwD,aAAa,CAAE,eAAe,CAC9BpE,UAAU,CAAE,YACd,CAAC,CAED;AACA,MAAO,MAAM,CAAAqE,mBAAmB,CAAG,CACjCC,cAAc,CAAE,gBAAgB,CAChCC,iBAAiB,CAAE,mBAAmB,CACtCC,eAAe,CAAE,iBAAiB,CAClCC,eAAe,CAAE,iBAAiB,CAClCC,sBAAsB,CAAE,wBAAwB,CAChDC,wBAAwB,CAAE,0BAC5B,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,iBAAiB,CAAG,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,GAAG,CAAC,CAEtD;AACA,MAAO,MAAM,CAAAC,qBAAqB,CAAG,CACnCC,UAAU,CAAE,CAAC,CACbC,YAAY,CAAE,EAAE,CAChBC,eAAe,CAAE,GAAG,CACpBC,YAAY,CAAE,GAChB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,uBAAuB,CAAG,CACrCC,gBAAgB,CAAE,SAAS,CAC3BC,YAAY,CAAE,WAAW,CACzBC,gBAAgB,CAAE,WAAW,CAC7BC,YAAY,CAAE,KAChB,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,UAAU,CAAG/H,OAAO,CAACC,GAAG,CAACI,QAAQ,GAAK,aAAa,CAEhE;AACA,cAAe,CACbP,gBAAgB,CAChBc,gBAAgB,CAChBW,aAAa,CACb+B,QAAQ,CACRU,cAAc,CACdC,iBAAiB,CACjBQ,gBAAgB,CAChBS,UAAU,CACVK,UAAU,CACVM,iBAAiB,CACjBQ,WAAW,CACXK,WAAW,CACXG,mBAAmB,CACnBO,iBAAiB,CACjBC,qBAAqB,CACrBK,uBAAuB,CACvBK,UACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}