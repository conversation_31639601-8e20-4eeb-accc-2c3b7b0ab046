{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\KuberaCardSection.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport KuberaCheckout from './KuberaCheckout';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst KuberaCardSection = () => {\n  _s();\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [showCheckout, setShowCheckout] = useState(false);\n\n  // Analytics integration\n  const analytics = useAnalytics();\n  useComponentTracking('KuberaCardSection');\n\n  // Kubera Card product images - using custom PNG designs\n  const productImages = ['/images/kubera-card-1.png', '/images/kubera-card-2.png', '/images/kubera-card-3.png', '/images/kubera-card-4.png'];\n\n  // Auto slider functionality\n  useEffect(() => {\n    // Track product view\n    analytics.trackKuberaCardView();\n    const interval = setInterval(() => {\n      setCurrentImageIndex(prevIndex => {\n        const newIndex = (prevIndex + 1) % productImages.length;\n\n        // Track image carousel interaction\n        analytics.trackEvent('product_image_carousel', {\n          event_category: 'product_interaction',\n          image_index: newIndex,\n          total_images: productImages.length\n        });\n        return newIndex;\n      });\n    }, 3000); // Change image every 3 seconds\n\n    return () => clearInterval(interval);\n  }, [productImages.length, analytics]);\n  const handleBuyNow = () => {\n    setShowCheckout(true);\n\n    // Track add to cart / begin checkout\n    analytics.trackKuberaCardAddToCart(2500); // Assuming price is 2500 LKR\n    analytics.trackCheckoutStep(1, 'begin_checkout');\n    analytics.trackEvent('begin_checkout', {\n      event_category: 'ecommerce',\n      currency: 'LKR',\n      value: 2500,\n      items: [{\n        item_id: 'kubera_card',\n        item_name: 'Kubera Card',\n        item_category: 'spiritual_products',\n        price: 2500,\n        quantity: 1\n      }]\n    });\n  };\n  const nextImage = () => {\n    setCurrentImageIndex(prevIndex => (prevIndex + 1) % productImages.length);\n  };\n  const prevImage = () => {\n    setCurrentImageIndex(prevIndex => prevIndex === 0 ? productImages.length - 1 : prevIndex - 1);\n  };\n  const goToImage = index => {\n    setCurrentImageIndex(index);\n  };\n  if (showCheckout) {\n    return /*#__PURE__*/_jsxDEV(KuberaCheckout, {\n      onClose: () => setShowCheckout(false)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 82,\n      columnNumber: 12\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"kubera-card-section\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"kubera-card-container dark-glass-card\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-glow\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 88,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"card-shine\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-header\",\n        children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n          className: \"product-title\",\n          children: \"\\uD83C\\uDFB4 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0D9A\\u0DCF\\u0DA9\\u0DCA\\u0DB4\\u0DAD\\u0DCA \\uD83C\\uDFB4\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 93,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"product-subtitle\",\n          children: \"\\u0DB0\\u0DB1\\u0DBA \\u0DC3\\u0DC4 \\u0DC3\\u0DB8\\u0DD8\\u0DAF\\u0DCA\\u0DB0\\u0DD2\\u0DBA \\u0D86\\u0D9A\\u0DBB\\u0DCA\\u0DC2\\u0DAB\\u0DBA \\u0D9A\\u0DBB\\u0D9C\\u0DD0\\u0DB1\\u0DD3\\u0DB8 \\u0DC3\\u0DB3\\u0DC4\\u0DCF \\u0D85\\u0DB4 \\u0DC0\\u0DD2\\u0DC3\\u0DD2\\u0DB1\\u0DCA \\u0DB6\\u0DBD\\u0D9C\\u0DB1\\u0DCA\\u0DC0\\u0DB1 \\u0DBD\\u0DAF, \\u0D94\\u0DB6\\u0DA7\\u0DB8 \\u0DC0\\u0DD9\\u0DB1\\u0DCA \\u0DC0\\u0DD6 \\u0DB8\\u0DD9\\u0DB8 \\u0DC0\\u0DD2\\u0DC1\\u0DDA\\u0DC2 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0D9A\\u0DCF\\u0DA9\\u0DCA\\u0DB4\\u0DAD \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DAF\\u0DD9\\u0DC0\\u0DD2\\u0DBA\\u0DB1\\u0DCA\\u0D9C\\u0DDA \\u0D86\\u0DC1\\u0DD2\\u0DBB\\u0DCA\\u0DC0\\u0DCF\\u0DAF\\u0DBA \\u0DC3\\u0DB8\\u0D9F \\u0DAF\\u0DD0\\u0DB1\\u0DCA\\u0DB8 \\u0DC0\\u0DD9\\u0DB1\\u0DCA\\u0D9A\\u0DBB\\u0DC0\\u0DCF \\u0D9C\\u0DB1\\u0DCA\\u0DB1.\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 94,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 92,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-slider\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"slider-container\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"slider-btn prev-btn\",\n            onClick: prevImage,\n            children: \"\\u2039\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 102,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"image-container\",\n            children: [/*#__PURE__*/_jsxDEV(\"img\", {\n              src: productImages[currentImageIndex],\n              alt: `කුබේර කාඩ්පත් ${currentImageIndex + 1}`,\n              className: \"product-image\",\n              onError: e => {\n                // Fallback to a placeholder if SVG doesn't load\n                e.target.src = `https://via.placeholder.com/400x300/1a1a2e/f4d03f?text=කුබේර+කාඩ්පත්+${currentImageIndex + 1}`;\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"image-overlay\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"overlay-content\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"image-counter\",\n                  children: [currentImageIndex + 1, \" / \", productImages.length]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 120,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 119,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 118,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 106,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"slider-btn next-btn\",\n            onClick: nextImage,\n            children: \"\\u203A\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 127,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 101,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"slider-dots\",\n          children: productImages.map((_, index) => /*#__PURE__*/_jsxDEV(\"button\", {\n            className: `dot ${index === currentImageIndex ? 'active' : ''}`,\n            onClick: () => goToImage(index)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 133,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"product-details\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-features\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\u2728\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 148,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-text\",\n              children: \"\\u0DC0\\u0DD2\\u0DC1\\u0DDA\\u0DC2 \\u0D9A\\u0DD4\\u0DB6\\u0DDA\\u0DBB \\u0DB8\\u0DB1\\u0DCA\\u0DAD\\u0DCA\\u200D\\u0DBB \\u0DC3\\u0DC4\\u0DD2\\u0DAD\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 147,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\uD83C\\uDFAF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-text\",\n              children: \"\\u0DB0\\u0DB1\\u0DBA \\u0D86\\u0D9A\\u0DBB\\u0DCA\\u0DC2\\u0DAB\\u0DBA \\u0D9A\\u0DBB\\u0DB1 \\u0DC1\\u0D9A\\u0DCA\\u0DAD\\u0DD2\\u0DBA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 153,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 151,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDEE1\\uFE0F\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 156,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-text\",\n              children: \"\\u0D8B\\u0DC3\\u0DC3\\u0DCA \\u0DAD\\u0DAD\\u0DCA\\u0DAD\\u0DCA\\u0DC0\\u0DBA\\u0DDA \\u0D9A\\u0DCF\\u0DA9\\u0DCA\\u0DB4\\u0DAD\\u0DCA\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 157,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 155,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"feature-item\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-icon\",\n              children: \"\\uD83D\\uDCE6\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 160,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"feature-text\",\n              children: \"\\u0D86\\u0DBB\\u0D9A\\u0DCA\\u0DC2\\u0DD2\\u0DAD \\u0D87\\u0DC3\\u0DD4\\u0DBB\\u0DD4\\u0DB8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 159,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"product-pricing\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"price-section\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"original-price\",\n              children: \"\\u0DBB\\u0DD4. 2,599\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 167,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"current-price\",\n              children: \"\\u0DBB\\u0DD4. 1,299\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 168,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"discount-badge\",\n              children: \"50% OFF\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 169,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 166,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"price-note\",\n            children: \"* \\u0DB1\\u0DDC\\u0DB8\\u0DD2\\u0DBD\\u0DDA \\u0D9C\\u0DD9\\u0DAF\\u0DBB \\u0D9C\\u0DD9\\u0DB1\\u0DCA\\u0DC0\\u0DCF \\u0DAF\\u0DD3\\u0DB8 (Cash on Delivery)\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 171,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 165,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 145,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"buy-now-section\",\n        children: [/*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"buy-now-btn\",\n          onClick: handleBuyNow,\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn-icon\",\n            children: \"\\uD83D\\uDED2\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 180,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn-text\",\n            children: \"\\u0DAF\\u0DD0\\u0DB1\\u0DCA\\u0DB8 \\u0DB8\\u0DD2\\u0DBD\\u0DAF\\u0DD3 \\u0D9C\\u0DB1\\u0DCA\\u0DB1\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"btn-arrow\",\n            children: \"\\u2192\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"payment-info\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"payment-method\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"payment-icon\",\n              children: \"\\uD83D\\uDCB3\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"payment-text\",\n              children: \"Cash on Delivery\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 188,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"delivery-info\",\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"delivery-icon\",\n              children: \"\\uD83D\\uDE9A\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 191,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"delivery-text\",\n              children: \"2-3 \\u0DAF\\u0DD2\\u0DB1\\u0D9A\\u0DD2\\u0DB1\\u0DCA \\u0D9C\\u0DD9\\u0DB1\\u0DCA\\u0DC0\\u0DCF \\u0DAF\\u0DD3\\u0DB8\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 190,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 178,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 87,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 86,\n    columnNumber: 5\n  }, this);\n};\n_s(KuberaCardSection, \"rC1Hb0fMqcFk01FYE4GgeYofUJs=\", false, function () {\n  return [useAnalytics, useComponentTracking];\n});\n_c = KuberaCardSection;\nexport default KuberaCardSection;\nvar _c;\n$RefreshReg$(_c, \"KuberaCardSection\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "KuberaCheckout", "useAnalytics", "useComponentTracking", "jsxDEV", "_jsxDEV", "KuberaCardSection", "_s", "currentImageIndex", "setCurrentImageIndex", "showCheckout", "setShowCheckout", "analytics", "productImages", "trackKuberaCardView", "interval", "setInterval", "prevIndex", "newIndex", "length", "trackEvent", "event_category", "image_index", "total_images", "clearInterval", "handleBuyNow", "trackKuberaCardAddToCart", "trackCheckoutStep", "currency", "value", "items", "item_id", "item_name", "item_category", "price", "quantity", "nextImage", "prevImage", "goToImage", "index", "onClose", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "className", "children", "onClick", "src", "alt", "onError", "e", "target", "map", "_", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/KuberaCardSection.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport KuberaCheckout from './KuberaCheckout';\nimport { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';\n\nconst KuberaCardSection = () => {\n  const [currentImageIndex, setCurrentImageIndex] = useState(0);\n  const [showCheckout, setShowCheckout] = useState(false);\n\n  // Analytics integration\n  const analytics = useAnalytics();\n  useComponentTracking('KuberaCardSection');\n\n  // Kubera Card product images - using custom PNG designs\n  const productImages = [\n    '/images/kubera-card-1.png',\n    '/images/kubera-card-2.png',\n    '/images/kubera-card-3.png',\n    '/images/kubera-card-4.png'\n  ];\n\n  // Auto slider functionality\n  useEffect(() => {\n    // Track product view\n    analytics.trackKuberaCardView();\n\n    const interval = setInterval(() => {\n      setCurrentImageIndex((prevIndex) => {\n        const newIndex = (prevIndex + 1) % productImages.length;\n\n        // Track image carousel interaction\n        analytics.trackEvent('product_image_carousel', {\n          event_category: 'product_interaction',\n          image_index: newIndex,\n          total_images: productImages.length\n        });\n\n        return newIndex;\n      });\n    }, 3000); // Change image every 3 seconds\n\n    return () => clearInterval(interval);\n  }, [productImages.length, analytics]);\n\n  const handleBuyNow = () => {\n    setShowCheckout(true);\n\n    // Track add to cart / begin checkout\n    analytics.trackKuberaCardAddToCart(2500); // Assuming price is 2500 LKR\n    analytics.trackCheckoutStep(1, 'begin_checkout');\n\n    analytics.trackEvent('begin_checkout', {\n      event_category: 'ecommerce',\n      currency: 'LKR',\n      value: 2500,\n      items: [{\n        item_id: 'kubera_card',\n        item_name: 'Kubera Card',\n        item_category: 'spiritual_products',\n        price: 2500,\n        quantity: 1\n      }]\n    });\n  };\n\n  const nextImage = () => {\n    setCurrentImageIndex((prevIndex) => \n      (prevIndex + 1) % productImages.length\n    );\n  };\n\n  const prevImage = () => {\n    setCurrentImageIndex((prevIndex) => \n      prevIndex === 0 ? productImages.length - 1 : prevIndex - 1\n    );\n  };\n\n  const goToImage = (index) => {\n    setCurrentImageIndex(index);\n  };\n\n  if (showCheckout) {\n    return <KuberaCheckout onClose={() => setShowCheckout(false)} />;\n  }\n\n  return (\n    <div className=\"kubera-card-section\">\n      <div className=\"kubera-card-container dark-glass-card\">\n        <div className=\"card-glow\"></div>\n        <div className=\"card-shine\"></div>\n\n        {/* Product Header */}\n        <div className=\"product-header\">\n          <h3 className=\"product-title\">🎴 කුබේර කාඩ්පත් 🎴</h3>\n          <p className=\"product-subtitle\">\n            ධනය සහ සමෘද්ධිය ආකර්ෂණය කරගැනීම සඳහා අප විසින් බලගන්වන ලද, ඔබටම වෙන් වූ මෙම විශේෂ කුබේර කාඩ්පත කුබේර දෙවියන්ගේ ආශිර්වාදය සමඟ දැන්ම වෙන්කරවා ගන්න.\n          </p>\n        </div>\n\n        {/* Product Image Slider */}\n        <div className=\"product-slider\">\n          <div className=\"slider-container\">\n            <button className=\"slider-btn prev-btn\" onClick={prevImage}>\n              ‹\n            </button>\n            \n            <div className=\"image-container\">\n              <img \n                src={productImages[currentImageIndex]} \n                alt={`කුබේර කාඩ්පත් ${currentImageIndex + 1}`}\n                className=\"product-image\"\n                onError={(e) => {\n                  // Fallback to a placeholder if SVG doesn't load\n                  e.target.src = `https://via.placeholder.com/400x300/1a1a2e/f4d03f?text=කුබේර+කාඩ්පත්+${currentImageIndex + 1}`;\n                }}\n              />\n              \n              {/* Image overlay with product info */}\n              <div className=\"image-overlay\">\n                <div className=\"overlay-content\">\n                  <span className=\"image-counter\">\n                    {currentImageIndex + 1} / {productImages.length}\n                  </span>\n                </div>\n              </div>\n            </div>\n            \n            <button className=\"slider-btn next-btn\" onClick={nextImage}>\n              ›\n            </button>\n          </div>\n\n          {/* Slider Dots */}\n          <div className=\"slider-dots\">\n            {productImages.map((_, index) => (\n              <button\n                key={index}\n                className={`dot ${index === currentImageIndex ? 'active' : ''}`}\n                onClick={() => goToImage(index)}\n              />\n            ))}\n          </div>\n        </div>\n\n        {/* Product Details */}\n        <div className=\"product-details\">\n          <div className=\"product-features\">\n            <div className=\"feature-item\">\n              <span className=\"feature-icon\">✨</span>\n              <span className=\"feature-text\">විශේෂ කුබේර මන්ත්‍ර සහිත</span>\n            </div>\n            <div className=\"feature-item\">\n              <span className=\"feature-icon\">🎯</span>\n              <span className=\"feature-text\">ධනය ආකර්ෂණය කරන ශක්තිය</span>\n            </div>\n            <div className=\"feature-item\">\n              <span className=\"feature-icon\">🛡️</span>\n              <span className=\"feature-text\">උසස් තත්ත්වයේ කාඩ්පත්</span>\n            </div>\n            <div className=\"feature-item\">\n              <span className=\"feature-icon\">📦</span>\n              <span className=\"feature-text\">ආරක්ෂිත ඇසුරුම</span>\n            </div>\n          </div>\n\n          <div className=\"product-pricing\">\n            <div className=\"price-section\">\n              <span className=\"original-price\">රු. 2,599</span>\n              <span className=\"current-price\">රු. 1,299</span>\n              <span className=\"discount-badge\">50% OFF</span>\n            </div>\n            <p className=\"price-note\">\n              * නොමිලේ ගෙදර ගෙන්වා දීම (Cash on Delivery)\n            </p>\n          </div>\n        </div>\n\n        {/* Buy Now Button */}\n        <div className=\"buy-now-section\">\n          <button className=\"buy-now-btn\" onClick={handleBuyNow}>\n            <span className=\"btn-icon\">🛒</span>\n            <span className=\"btn-text\">දැන්ම මිලදී ගන්න</span>\n            <span className=\"btn-arrow\">→</span>\n          </button>\n          \n          <div className=\"payment-info\">\n            <div className=\"payment-method\">\n              <span className=\"payment-icon\">💳</span>\n              <span className=\"payment-text\">Cash on Delivery</span>\n            </div>\n            <div className=\"delivery-info\">\n              <span className=\"delivery-icon\">🚚</span>\n              <span className=\"delivery-text\">2-3 දිනකින් ගෙන්වා දීම</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\n\n\nexport default KuberaCardSection;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,SAASC,YAAY,EAAEC,oBAAoB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3E,MAAMC,iBAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGV,QAAQ,CAAC,CAAC,CAAC;EAC7D,MAAM,CAACW,YAAY,EAAEC,eAAe,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;;EAEvD;EACA,MAAMa,SAAS,GAAGV,YAAY,CAAC,CAAC;EAChCC,oBAAoB,CAAC,mBAAmB,CAAC;;EAEzC;EACA,MAAMU,aAAa,GAAG,CACpB,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,EAC3B,2BAA2B,CAC5B;;EAED;EACAb,SAAS,CAAC,MAAM;IACd;IACAY,SAAS,CAACE,mBAAmB,CAAC,CAAC;IAE/B,MAAMC,QAAQ,GAAGC,WAAW,CAAC,MAAM;MACjCP,oBAAoB,CAAEQ,SAAS,IAAK;QAClC,MAAMC,QAAQ,GAAG,CAACD,SAAS,GAAG,CAAC,IAAIJ,aAAa,CAACM,MAAM;;QAEvD;QACAP,SAAS,CAACQ,UAAU,CAAC,wBAAwB,EAAE;UAC7CC,cAAc,EAAE,qBAAqB;UACrCC,WAAW,EAAEJ,QAAQ;UACrBK,YAAY,EAAEV,aAAa,CAACM;QAC9B,CAAC,CAAC;QAEF,OAAOD,QAAQ;MACjB,CAAC,CAAC;IACJ,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC;;IAEV,OAAO,MAAMM,aAAa,CAACT,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACF,aAAa,CAACM,MAAM,EAAEP,SAAS,CAAC,CAAC;EAErC,MAAMa,YAAY,GAAGA,CAAA,KAAM;IACzBd,eAAe,CAAC,IAAI,CAAC;;IAErB;IACAC,SAAS,CAACc,wBAAwB,CAAC,IAAI,CAAC,CAAC,CAAC;IAC1Cd,SAAS,CAACe,iBAAiB,CAAC,CAAC,EAAE,gBAAgB,CAAC;IAEhDf,SAAS,CAACQ,UAAU,CAAC,gBAAgB,EAAE;MACrCC,cAAc,EAAE,WAAW;MAC3BO,QAAQ,EAAE,KAAK;MACfC,KAAK,EAAE,IAAI;MACXC,KAAK,EAAE,CAAC;QACNC,OAAO,EAAE,aAAa;QACtBC,SAAS,EAAE,aAAa;QACxBC,aAAa,EAAE,oBAAoB;QACnCC,KAAK,EAAE,IAAI;QACXC,QAAQ,EAAE;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,SAAS,GAAGA,CAAA,KAAM;IACtB3B,oBAAoB,CAAEQ,SAAS,IAC7B,CAACA,SAAS,GAAG,CAAC,IAAIJ,aAAa,CAACM,MAClC,CAAC;EACH,CAAC;EAED,MAAMkB,SAAS,GAAGA,CAAA,KAAM;IACtB5B,oBAAoB,CAAEQ,SAAS,IAC7BA,SAAS,KAAK,CAAC,GAAGJ,aAAa,CAACM,MAAM,GAAG,CAAC,GAAGF,SAAS,GAAG,CAC3D,CAAC;EACH,CAAC;EAED,MAAMqB,SAAS,GAAIC,KAAK,IAAK;IAC3B9B,oBAAoB,CAAC8B,KAAK,CAAC;EAC7B,CAAC;EAED,IAAI7B,YAAY,EAAE;IAChB,oBAAOL,OAAA,CAACJ,cAAc;MAACuC,OAAO,EAAEA,CAAA,KAAM7B,eAAe,CAAC,KAAK;IAAE;MAAA8B,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAClE;EAEA,oBACEvC,OAAA;IAAKwC,SAAS,EAAC,qBAAqB;IAAAC,QAAA,eAClCzC,OAAA;MAAKwC,SAAS,EAAC,uCAAuC;MAAAC,QAAA,gBACpDzC,OAAA;QAAKwC,SAAS,EAAC;MAAW;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eACjCvC,OAAA;QAAKwC,SAAS,EAAC;MAAY;QAAAJ,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC,eAGlCvC,OAAA;QAAKwC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzC,OAAA;UAAIwC,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAC;QAAmB;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACtDvC,OAAA;UAAGwC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,EAAC;QAEhC;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAGNvC,OAAA;QAAKwC,SAAS,EAAC,gBAAgB;QAAAC,QAAA,gBAC7BzC,OAAA;UAAKwC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BzC,OAAA;YAAQwC,SAAS,EAAC,qBAAqB;YAACE,OAAO,EAAEV,SAAU;YAAAS,QAAA,EAAC;UAE5D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETvC,OAAA;YAAKwC,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BzC,OAAA;cACE2C,GAAG,EAAEnC,aAAa,CAACL,iBAAiB,CAAE;cACtCyC,GAAG,EAAE,iBAAiBzC,iBAAiB,GAAG,CAAC,EAAG;cAC9CqC,SAAS,EAAC,eAAe;cACzBK,OAAO,EAAGC,CAAC,IAAK;gBACd;gBACAA,CAAC,CAACC,MAAM,CAACJ,GAAG,GAAG,wEAAwExC,iBAAiB,GAAG,CAAC,EAAE;cAChH;YAAE;cAAAiC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAGFvC,OAAA;cAAKwC,SAAS,EAAC,eAAe;cAAAC,QAAA,eAC5BzC,OAAA;gBAAKwC,SAAS,EAAC,iBAAiB;gBAAAC,QAAA,eAC9BzC,OAAA;kBAAMwC,SAAS,EAAC,eAAe;kBAAAC,QAAA,GAC5BtC,iBAAiB,GAAG,CAAC,EAAC,KAAG,EAACK,aAAa,CAACM,MAAM;gBAAA;kBAAAsB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC3C;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACJ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAENvC,OAAA;YAAQwC,SAAS,EAAC,qBAAqB;YAACE,OAAO,EAAEX,SAAU;YAAAU,QAAA,EAAC;UAE5D;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNvC,OAAA;UAAKwC,SAAS,EAAC,aAAa;UAAAC,QAAA,EACzBjC,aAAa,CAACwC,GAAG,CAAC,CAACC,CAAC,EAAEf,KAAK,kBAC1BlC,OAAA;YAEEwC,SAAS,EAAE,OAAON,KAAK,KAAK/B,iBAAiB,GAAG,QAAQ,GAAG,EAAE,EAAG;YAChEuC,OAAO,EAAEA,CAAA,KAAMT,SAAS,CAACC,KAAK;UAAE,GAF3BA,KAAK;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAGX,CACF;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvC,OAAA;QAAKwC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzC,OAAA;UAAKwC,SAAS,EAAC,kBAAkB;UAAAC,QAAA,gBAC/BzC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAC;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvCvC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAwB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3D,CAAC,eACNvC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCvC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAsB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACzD,CAAC,eACNvC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAG;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCvC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAqB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACxD,CAAC,eACNvC,OAAA;YAAKwC,SAAS,EAAC,cAAc;YAAAC,QAAA,gBAC3BzC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCvC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAc;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACjD,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENvC,OAAA;UAAKwC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BzC,OAAA;YAAKwC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BzC,OAAA;cAAMwC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACjDvC,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAS;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eAChDvC,OAAA;cAAMwC,SAAS,EAAC,gBAAgB;cAAAC,QAAA,EAAC;YAAO;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5C,CAAC,eACNvC,OAAA;YAAGwC,SAAS,EAAC,YAAY;YAAAC,QAAA,EAAC;UAE1B;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvC,OAAA;QAAKwC,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BzC,OAAA;UAAQwC,SAAS,EAAC,aAAa;UAACE,OAAO,EAAEtB,YAAa;UAAAqB,QAAA,gBACpDzC,OAAA;YAAMwC,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAE;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACpCvC,OAAA;YAAMwC,SAAS,EAAC,UAAU;YAAAC,QAAA,EAAC;UAAgB;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDvC,OAAA;YAAMwC,SAAS,EAAC,WAAW;YAAAC,QAAA,EAAC;UAAC;YAAAL,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9B,CAAC,eAETvC,OAAA;UAAKwC,SAAS,EAAC,cAAc;UAAAC,QAAA,gBAC3BzC,OAAA;YAAKwC,SAAS,EAAC,gBAAgB;YAAAC,QAAA,gBAC7BzC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACxCvC,OAAA;cAAMwC,SAAS,EAAC,cAAc;cAAAC,QAAA,EAAC;YAAgB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnD,CAAC,eACNvC,OAAA;YAAKwC,SAAS,EAAC,eAAe;YAAAC,QAAA,gBAC5BzC,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAE;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACzCvC,OAAA;cAAMwC,SAAS,EAAC,eAAe;cAAAC,QAAA,EAAC;YAAsB;cAAAL,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrC,EAAA,CAlMID,iBAAiB;EAAA,QAKHJ,YAAY,EAC9BC,oBAAoB;AAAA;AAAAoD,EAAA,GANhBjD,iBAAiB;AAsMvB,eAAeA,iBAAiB;AAAC,IAAAiD,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}