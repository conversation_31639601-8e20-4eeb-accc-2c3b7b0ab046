{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Horoscope\\\\src\\\\components\\\\AnalyticsDebugger.js\",\n  _s = $RefreshSig$();\n/**\n * Analytics Debugger Component\n * Shows analytics status and recent events for development/debugging\n * Only visible in development mode or when explicitly enabled\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { ANALYTICS_CONFIG, DEBUG_MODE } from '../config/analytics';\nimport { GA_MEASUREMENT_ID } from '../services/analytics';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst AnalyticsDebugger = ({\n  enabled = DEBUG_MODE\n}) => {\n  _s();\n  const [isVisible, setIsVisible] = useState(false);\n  const [events, setEvents] = useState([]);\n  const [gaStatus, setGaStatus] = useState('checking');\n  useEffect(() => {\n    if (!enabled) return;\n\n    // Check GA status\n    const checkGAStatus = () => {\n      if (typeof window !== 'undefined') {\n        if (typeof window.gtag === 'function') {\n          setGaStatus('loaded');\n        } else if (typeof window.initializeGA === 'function') {\n          setGaStatus('ready');\n        } else {\n          setGaStatus('not_loaded');\n        }\n      }\n    };\n    checkGAStatus();\n    const interval = setInterval(checkGAStatus, 2000);\n\n    // Intercept gtag calls to show events\n    if (typeof window !== 'undefined' && window.gtag) {\n      const originalGtag = window.gtag;\n      window.gtag = function (...args) {\n        // Log the event\n        if (args[0] === 'event') {\n          const eventName = args[1];\n          const eventData = args[2] || {};\n          setEvents(prev => [{\n            timestamp: new Date().toLocaleTimeString(),\n            type: 'event',\n            name: eventName,\n            data: eventData\n          }, ...prev.slice(0, 9)]); // Keep last 10 events\n        } else if (args[0] === 'config') {\n          setEvents(prev => [{\n            timestamp: new Date().toLocaleTimeString(),\n            type: 'config',\n            name: 'GA Config',\n            data: args[2] || {}\n          }, ...prev.slice(0, 9)]);\n        }\n\n        // Call original gtag\n        return originalGtag.apply(this, args);\n      };\n    }\n    return () => clearInterval(interval);\n  }, [enabled]);\n  if (!enabled) return null;\n  const getStatusColor = () => {\n    switch (gaStatus) {\n      case 'loaded':\n        return '#4CAF50';\n      case 'ready':\n        return '#FF9800';\n      case 'not_loaded':\n        return '#F44336';\n      default:\n        return '#9E9E9E';\n    }\n  };\n  const getStatusText = () => {\n    switch (gaStatus) {\n      case 'loaded':\n        return 'GA4 Loaded & Active';\n      case 'ready':\n        return 'GA4 Ready (Not Initialized)';\n      case 'not_loaded':\n        return 'GA4 Not Loaded';\n      default:\n        return 'Checking...';\n    }\n  };\n  const debuggerStyles = {\n    position: 'fixed',\n    bottom: isVisible ? '0' : '-300px',\n    right: '20px',\n    width: '400px',\n    maxHeight: '400px',\n    backgroundColor: 'rgba(0, 0, 0, 0.9)',\n    color: 'white',\n    borderRadius: '8px 8px 0 0',\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.3)',\n    transition: 'bottom 0.3s ease',\n    zIndex: 10000,\n    fontFamily: 'monospace',\n    fontSize: '12px'\n  };\n  const headerStyles = {\n    padding: '10px 15px',\n    backgroundColor: 'rgba(255, 255, 255, 0.1)',\n    borderBottom: '1px solid rgba(255, 255, 255, 0.2)',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    cursor: 'pointer'\n  };\n  const contentStyles = {\n    padding: '15px',\n    maxHeight: '300px',\n    overflowY: 'auto'\n  };\n  const statusStyles = {\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    marginBottom: '15px'\n  };\n  const statusDotStyles = {\n    width: '8px',\n    height: '8px',\n    borderRadius: '50%',\n    backgroundColor: getStatusColor()\n  };\n  const eventStyles = {\n    marginBottom: '8px',\n    padding: '8px',\n    backgroundColor: 'rgba(255, 255, 255, 0.05)',\n    borderRadius: '4px',\n    borderLeft: '3px solid #2196F3'\n  };\n  const toggleButtonStyles = {\n    position: 'fixed',\n    bottom: '20px',\n    right: '20px',\n    width: '50px',\n    height: '50px',\n    borderRadius: '50%',\n    backgroundColor: '#2196F3',\n    color: 'white',\n    border: 'none',\n    cursor: 'pointer',\n    fontSize: '18px',\n    zIndex: 10001,\n    boxShadow: '0 4px 12px rgba(33, 150, 243, 0.3)'\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"button\", {\n      style: toggleButtonStyles,\n      onClick: () => setIsVisible(!isVisible),\n      title: \"Analytics Debugger\",\n      children: \"\\uD83D\\uDCCA\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 159,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: debuggerStyles,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        style: headerStyles,\n        onClick: () => setIsVisible(!isVisible),\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          children: \"\\uD83D\\uDCCA Analytics Debugger\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          children: isVisible ? '▼' : '▲'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 171,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 169,\n        columnNumber: 9\n      }, this), isVisible && /*#__PURE__*/_jsxDEV(\"div\", {\n        style: contentStyles,\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: statusStyles,\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: statusDotStyles\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 178,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            children: getStatusText()\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 179,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 177,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginBottom: '15px',\n            fontSize: '11px',\n            opacity: 0.8\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Measurement ID: \", GA_MEASUREMENT_ID || 'Not Set']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 184,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Environment: \", process.env.NODE_ENV]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Debug Mode: \", DEBUG_MODE ? 'ON' : 'OFF']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            children: [\"Dev Analytics: \", ANALYTICS_CONFIG.enableInDevelopment ? 'ON' : 'OFF']\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 187,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 183,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              fontWeight: 'bold'\n            },\n            children: \"Recent Events:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 15\n          }, this), events.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              opacity: 0.6,\n              fontStyle: 'italic'\n            },\n            children: \"No events tracked yet. Interact with the website to see events.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 194,\n            columnNumber: 17\n          }, this) : events.map((event, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n            style: eventStyles,\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                display: 'flex',\n                justifyContent: 'space-between',\n                marginBottom: '4px'\n              },\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  fontWeight: 'bold',\n                  color: '#4CAF50'\n                },\n                children: event.name\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 201,\n                columnNumber: 23\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                style: {\n                  opacity: 0.7\n                },\n                children: event.timestamp\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 204,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 21\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              style: {\n                fontSize: '10px',\n                opacity: 0.8\n              },\n              children: Object.keys(event.data).length > 0 ? Object.entries(event.data).slice(0, 3).map(([key, value]) => /*#__PURE__*/_jsxDEV(\"div\", {\n                children: [key, \": \", typeof value === 'object' ? JSON.stringify(value).slice(0, 30) + '...' : String(value).slice(0, 30)]\n              }, key, true, {\n                fileName: _jsxFileName,\n                lineNumber: 209,\n                columnNumber: 27\n              }, this)) : 'No parameters'\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 21\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 19\n          }, this))]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 191,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            marginTop: '15px',\n            paddingTop: '15px',\n            borderTop: '1px solid rgba(255, 255, 255, 0.2)'\n          },\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            style: {\n              marginBottom: '8px',\n              fontWeight: 'bold'\n            },\n            children: \"Quick Actions:\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              backgroundColor: '#4CAF50',\n              color: 'white',\n              border: 'none',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              marginRight: '8px',\n              fontSize: '10px'\n            },\n            onClick: () => {\n              if (window.gtag) {\n                window.gtag('event', 'debug_test', {\n                  event_category: 'debug',\n                  event_label: 'manual_test'\n                });\n              }\n            },\n            children: \"Send Test Event\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 225,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            style: {\n              backgroundColor: '#FF9800',\n              color: 'white',\n              border: 'none',\n              padding: '4px 8px',\n              borderRadius: '4px',\n              cursor: 'pointer',\n              fontSize: '10px'\n            },\n            onClick: () => setEvents([]),\n            children: \"Clear Events\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 247,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 223,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 175,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 168,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true);\n};\n_s(AnalyticsDebugger, \"OTI68djKJjV6r1TQlIT59tvrtVo=\");\n_c = AnalyticsDebugger;\nexport default AnalyticsDebugger;\nvar _c;\n$RefreshReg$(_c, \"AnalyticsDebugger\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "ANALYTICS_CONFIG", "DEBUG_MODE", "GA_MEASUREMENT_ID", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "AnalyticsDebugger", "enabled", "_s", "isVisible", "setIsVisible", "events", "setEvents", "gaStatus", "setGaStatus", "checkGAStatus", "window", "gtag", "initializeGA", "interval", "setInterval", "originalGtag", "args", "eventName", "eventData", "prev", "timestamp", "Date", "toLocaleTimeString", "type", "name", "data", "slice", "apply", "clearInterval", "getStatusColor", "getStatusText", "debuggerStyles", "position", "bottom", "right", "width", "maxHeight", "backgroundColor", "color", "borderRadius", "boxShadow", "transition", "zIndex", "fontFamily", "fontSize", "headerStyles", "padding", "borderBottom", "display", "justifyContent", "alignItems", "cursor", "contentStyles", "overflowY", "statusStyles", "gap", "marginBottom", "statusDotStyles", "height", "eventStyles", "borderLeft", "toggleButtonStyles", "border", "children", "style", "onClick", "title", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "opacity", "process", "env", "NODE_ENV", "enableInDevelopment", "fontWeight", "length", "fontStyle", "map", "event", "index", "Object", "keys", "entries", "key", "value", "JSON", "stringify", "String", "marginTop", "paddingTop", "borderTop", "marginRight", "event_category", "event_label", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/components/AnalyticsDebugger.js"], "sourcesContent": ["/**\n * Analytics Debugger Component\n * Shows analytics status and recent events for development/debugging\n * Only visible in development mode or when explicitly enabled\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { ANALYTICS_CONFIG, DEBUG_MODE } from '../config/analytics';\nimport { GA_MEASUREMENT_ID } from '../services/analytics';\n\nconst AnalyticsDebugger = ({ enabled = DEBUG_MODE }) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [events, setEvents] = useState([]);\n  const [gaStatus, setGaStatus] = useState('checking');\n\n  useEffect(() => {\n    if (!enabled) return;\n\n    // Check GA status\n    const checkGAStatus = () => {\n      if (typeof window !== 'undefined') {\n        if (typeof window.gtag === 'function') {\n          setGaStatus('loaded');\n        } else if (typeof window.initializeGA === 'function') {\n          setGaStatus('ready');\n        } else {\n          setGaStatus('not_loaded');\n        }\n      }\n    };\n\n    checkGAStatus();\n    const interval = setInterval(checkGAStatus, 2000);\n\n    // Intercept gtag calls to show events\n    if (typeof window !== 'undefined' && window.gtag) {\n      const originalGtag = window.gtag;\n      window.gtag = function(...args) {\n        // Log the event\n        if (args[0] === 'event') {\n          const eventName = args[1];\n          const eventData = args[2] || {};\n          setEvents(prev => [{\n            timestamp: new Date().toLocaleTimeString(),\n            type: 'event',\n            name: eventName,\n            data: eventData\n          }, ...prev.slice(0, 9)]); // Keep last 10 events\n        } else if (args[0] === 'config') {\n          setEvents(prev => [{\n            timestamp: new Date().toLocaleTimeString(),\n            type: 'config',\n            name: 'GA Config',\n            data: args[2] || {}\n          }, ...prev.slice(0, 9)]);\n        }\n        \n        // Call original gtag\n        return originalGtag.apply(this, args);\n      };\n    }\n\n    return () => clearInterval(interval);\n  }, [enabled]);\n\n  if (!enabled) return null;\n\n  const getStatusColor = () => {\n    switch (gaStatus) {\n      case 'loaded': return '#4CAF50';\n      case 'ready': return '#FF9800';\n      case 'not_loaded': return '#F44336';\n      default: return '#9E9E9E';\n    }\n  };\n\n  const getStatusText = () => {\n    switch (gaStatus) {\n      case 'loaded': return 'GA4 Loaded & Active';\n      case 'ready': return 'GA4 Ready (Not Initialized)';\n      case 'not_loaded': return 'GA4 Not Loaded';\n      default: return 'Checking...';\n    }\n  };\n\n  const debuggerStyles = {\n    position: 'fixed',\n    bottom: isVisible ? '0' : '-300px',\n    right: '20px',\n    width: '400px',\n    maxHeight: '400px',\n    backgroundColor: 'rgba(0, 0, 0, 0.9)',\n    color: 'white',\n    borderRadius: '8px 8px 0 0',\n    boxShadow: '0 -4px 20px rgba(0, 0, 0, 0.3)',\n    transition: 'bottom 0.3s ease',\n    zIndex: 10000,\n    fontFamily: 'monospace',\n    fontSize: '12px'\n  };\n\n  const headerStyles = {\n    padding: '10px 15px',\n    backgroundColor: 'rgba(255, 255, 255, 0.1)',\n    borderBottom: '1px solid rgba(255, 255, 255, 0.2)',\n    display: 'flex',\n    justifyContent: 'space-between',\n    alignItems: 'center',\n    cursor: 'pointer'\n  };\n\n  const contentStyles = {\n    padding: '15px',\n    maxHeight: '300px',\n    overflowY: 'auto'\n  };\n\n  const statusStyles = {\n    display: 'flex',\n    alignItems: 'center',\n    gap: '8px',\n    marginBottom: '15px'\n  };\n\n  const statusDotStyles = {\n    width: '8px',\n    height: '8px',\n    borderRadius: '50%',\n    backgroundColor: getStatusColor()\n  };\n\n  const eventStyles = {\n    marginBottom: '8px',\n    padding: '8px',\n    backgroundColor: 'rgba(255, 255, 255, 0.05)',\n    borderRadius: '4px',\n    borderLeft: '3px solid #2196F3'\n  };\n\n  const toggleButtonStyles = {\n    position: 'fixed',\n    bottom: '20px',\n    right: '20px',\n    width: '50px',\n    height: '50px',\n    borderRadius: '50%',\n    backgroundColor: '#2196F3',\n    color: 'white',\n    border: 'none',\n    cursor: 'pointer',\n    fontSize: '18px',\n    zIndex: 10001,\n    boxShadow: '0 4px 12px rgba(33, 150, 243, 0.3)'\n  };\n\n  return (\n    <>\n      {/* Toggle Button */}\n      <button\n        style={toggleButtonStyles}\n        onClick={() => setIsVisible(!isVisible)}\n        title=\"Analytics Debugger\"\n      >\n        📊\n      </button>\n\n      {/* Debugger Panel */}\n      <div style={debuggerStyles}>\n        <div style={headerStyles} onClick={() => setIsVisible(!isVisible)}>\n          <span>📊 Analytics Debugger</span>\n          <span>{isVisible ? '▼' : '▲'}</span>\n        </div>\n\n        {isVisible && (\n          <div style={contentStyles}>\n            {/* Status Section */}\n            <div style={statusStyles}>\n              <div style={statusDotStyles}></div>\n              <span>{getStatusText()}</span>\n            </div>\n\n            {/* Configuration Info */}\n            <div style={{ marginBottom: '15px', fontSize: '11px', opacity: 0.8 }}>\n              <div>Measurement ID: {GA_MEASUREMENT_ID || 'Not Set'}</div>\n              <div>Environment: {process.env.NODE_ENV}</div>\n              <div>Debug Mode: {DEBUG_MODE ? 'ON' : 'OFF'}</div>\n              <div>Dev Analytics: {ANALYTICS_CONFIG.enableInDevelopment ? 'ON' : 'OFF'}</div>\n            </div>\n\n            {/* Recent Events */}\n            <div>\n              <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>Recent Events:</div>\n              {events.length === 0 ? (\n                <div style={{ opacity: 0.6, fontStyle: 'italic' }}>\n                  No events tracked yet. Interact with the website to see events.\n                </div>\n              ) : (\n                events.map((event, index) => (\n                  <div key={index} style={eventStyles}>\n                    <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '4px' }}>\n                      <span style={{ fontWeight: 'bold', color: '#4CAF50' }}>\n                        {event.name}\n                      </span>\n                      <span style={{ opacity: 0.7 }}>{event.timestamp}</span>\n                    </div>\n                    <div style={{ fontSize: '10px', opacity: 0.8 }}>\n                      {Object.keys(event.data).length > 0 ? (\n                        Object.entries(event.data).slice(0, 3).map(([key, value]) => (\n                          <div key={key}>\n                            {key}: {typeof value === 'object' ? JSON.stringify(value).slice(0, 30) + '...' : String(value).slice(0, 30)}\n                          </div>\n                        ))\n                      ) : (\n                        'No parameters'\n                      )}\n                    </div>\n                  </div>\n                ))\n              )}\n            </div>\n\n            {/* Quick Actions */}\n            <div style={{ marginTop: '15px', paddingTop: '15px', borderTop: '1px solid rgba(255, 255, 255, 0.2)' }}>\n              <div style={{ marginBottom: '8px', fontWeight: 'bold' }}>Quick Actions:</div>\n              <button\n                style={{\n                  backgroundColor: '#4CAF50',\n                  color: 'white',\n                  border: 'none',\n                  padding: '4px 8px',\n                  borderRadius: '4px',\n                  cursor: 'pointer',\n                  marginRight: '8px',\n                  fontSize: '10px'\n                }}\n                onClick={() => {\n                  if (window.gtag) {\n                    window.gtag('event', 'debug_test', {\n                      event_category: 'debug',\n                      event_label: 'manual_test'\n                    });\n                  }\n                }}\n              >\n                Send Test Event\n              </button>\n              <button\n                style={{\n                  backgroundColor: '#FF9800',\n                  color: 'white',\n                  border: 'none',\n                  padding: '4px 8px',\n                  borderRadius: '4px',\n                  cursor: 'pointer',\n                  fontSize: '10px'\n                }}\n                onClick={() => setEvents([])}\n              >\n                Clear Events\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n    </>\n  );\n};\n\nexport default AnalyticsDebugger;\n"], "mappings": ";;AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,gBAAgB,EAAEC,UAAU,QAAQ,qBAAqB;AAClE,SAASC,iBAAiB,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAE1D,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,OAAO,GAAGP;AAAW,CAAC,KAAK;EAAAQ,EAAA;EACtD,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EACjD,MAAM,CAACc,MAAM,EAAEC,SAAS,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACgB,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,UAAU,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACd,IAAI,CAACS,OAAO,EAAE;;IAEd;IACA,MAAMQ,aAAa,GAAGA,CAAA,KAAM;MAC1B,IAAI,OAAOC,MAAM,KAAK,WAAW,EAAE;QACjC,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU,EAAE;UACrCH,WAAW,CAAC,QAAQ,CAAC;QACvB,CAAC,MAAM,IAAI,OAAOE,MAAM,CAACE,YAAY,KAAK,UAAU,EAAE;UACpDJ,WAAW,CAAC,OAAO,CAAC;QACtB,CAAC,MAAM;UACLA,WAAW,CAAC,YAAY,CAAC;QAC3B;MACF;IACF,CAAC;IAEDC,aAAa,CAAC,CAAC;IACf,MAAMI,QAAQ,GAAGC,WAAW,CAACL,aAAa,EAAE,IAAI,CAAC;;IAEjD;IACA,IAAI,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,IAAI,EAAE;MAChD,MAAMI,YAAY,GAAGL,MAAM,CAACC,IAAI;MAChCD,MAAM,CAACC,IAAI,GAAG,UAAS,GAAGK,IAAI,EAAE;QAC9B;QACA,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,OAAO,EAAE;UACvB,MAAMC,SAAS,GAAGD,IAAI,CAAC,CAAC,CAAC;UACzB,MAAME,SAAS,GAAGF,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UAC/BV,SAAS,CAACa,IAAI,IAAI,CAAC;YACjBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAC1CC,IAAI,EAAE,OAAO;YACbC,IAAI,EAAEP,SAAS;YACfQ,IAAI,EAAEP;UACR,CAAC,EAAE,GAAGC,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAC5B,CAAC,MAAM,IAAIV,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;UAC/BV,SAAS,CAACa,IAAI,IAAI,CAAC;YACjBC,SAAS,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC;YAC1CC,IAAI,EAAE,QAAQ;YACdC,IAAI,EAAE,WAAW;YACjBC,IAAI,EAAET,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC;UACpB,CAAC,EAAE,GAAGG,IAAI,CAACO,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QAC1B;;QAEA;QACA,OAAOX,YAAY,CAACY,KAAK,CAAC,IAAI,EAAEX,IAAI,CAAC;MACvC,CAAC;IACH;IAEA,OAAO,MAAMY,aAAa,CAACf,QAAQ,CAAC;EACtC,CAAC,EAAE,CAACZ,OAAO,CAAC,CAAC;EAEb,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;EAEzB,MAAM4B,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQtB,QAAQ;MACd,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,YAAY;QAAE,OAAO,SAAS;MACnC;QAAS,OAAO,SAAS;IAC3B;EACF,CAAC;EAED,MAAMuB,aAAa,GAAGA,CAAA,KAAM;IAC1B,QAAQvB,QAAQ;MACd,KAAK,QAAQ;QAAE,OAAO,qBAAqB;MAC3C,KAAK,OAAO;QAAE,OAAO,6BAA6B;MAClD,KAAK,YAAY;QAAE,OAAO,gBAAgB;MAC1C;QAAS,OAAO,aAAa;IAC/B;EACF,CAAC;EAED,MAAMwB,cAAc,GAAG;IACrBC,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE9B,SAAS,GAAG,GAAG,GAAG,QAAQ;IAClC+B,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,OAAO;IACdC,SAAS,EAAE,OAAO;IAClBC,eAAe,EAAE,oBAAoB;IACrCC,KAAK,EAAE,OAAO;IACdC,YAAY,EAAE,aAAa;IAC3BC,SAAS,EAAE,gCAAgC;IAC3CC,UAAU,EAAE,kBAAkB;IAC9BC,MAAM,EAAE,KAAK;IACbC,UAAU,EAAE,WAAW;IACvBC,QAAQ,EAAE;EACZ,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBC,OAAO,EAAE,WAAW;IACpBT,eAAe,EAAE,0BAA0B;IAC3CU,YAAY,EAAE,oCAAoC;IAClDC,OAAO,EAAE,MAAM;IACfC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE,QAAQ;IACpBC,MAAM,EAAE;EACV,CAAC;EAED,MAAMC,aAAa,GAAG;IACpBN,OAAO,EAAE,MAAM;IACfV,SAAS,EAAE,OAAO;IAClBiB,SAAS,EAAE;EACb,CAAC;EAED,MAAMC,YAAY,GAAG;IACnBN,OAAO,EAAE,MAAM;IACfE,UAAU,EAAE,QAAQ;IACpBK,GAAG,EAAE,KAAK;IACVC,YAAY,EAAE;EAChB,CAAC;EAED,MAAMC,eAAe,GAAG;IACtBtB,KAAK,EAAE,KAAK;IACZuB,MAAM,EAAE,KAAK;IACbnB,YAAY,EAAE,KAAK;IACnBF,eAAe,EAAER,cAAc,CAAC;EAClC,CAAC;EAED,MAAM8B,WAAW,GAAG;IAClBH,YAAY,EAAE,KAAK;IACnBV,OAAO,EAAE,KAAK;IACdT,eAAe,EAAE,2BAA2B;IAC5CE,YAAY,EAAE,KAAK;IACnBqB,UAAU,EAAE;EACd,CAAC;EAED,MAAMC,kBAAkB,GAAG;IACzB7B,QAAQ,EAAE,OAAO;IACjBC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IACbC,KAAK,EAAE,MAAM;IACbuB,MAAM,EAAE,MAAM;IACdnB,YAAY,EAAE,KAAK;IACnBF,eAAe,EAAE,SAAS;IAC1BC,KAAK,EAAE,OAAO;IACdwB,MAAM,EAAE,MAAM;IACdX,MAAM,EAAE,SAAS;IACjBP,QAAQ,EAAE,MAAM;IAChBF,MAAM,EAAE,KAAK;IACbF,SAAS,EAAE;EACb,CAAC;EAED,oBACE3C,OAAA,CAAAE,SAAA;IAAAgE,QAAA,gBAEElE,OAAA;MACEmE,KAAK,EAAEH,kBAAmB;MAC1BI,OAAO,EAAEA,CAAA,KAAM7D,YAAY,CAAC,CAACD,SAAS,CAAE;MACxC+D,KAAK,EAAC,oBAAoB;MAAAH,QAAA,EAC3B;IAED;MAAAI,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAQ,CAAC,eAGTzE,OAAA;MAAKmE,KAAK,EAAEjC,cAAe;MAAAgC,QAAA,gBACzBlE,OAAA;QAAKmE,KAAK,EAAEnB,YAAa;QAACoB,OAAO,EAAEA,CAAA,KAAM7D,YAAY,CAAC,CAACD,SAAS,CAAE;QAAA4D,QAAA,gBAChElE,OAAA;UAAAkE,QAAA,EAAM;QAAqB;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAClCzE,OAAA;UAAAkE,QAAA,EAAO5D,SAAS,GAAG,GAAG,GAAG;QAAG;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjC,CAAC,EAELnE,SAAS,iBACRN,OAAA;QAAKmE,KAAK,EAAEZ,aAAc;QAAAW,QAAA,gBAExBlE,OAAA;UAAKmE,KAAK,EAAEV,YAAa;UAAAS,QAAA,gBACvBlE,OAAA;YAAKmE,KAAK,EAAEP;UAAgB;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACnCzE,OAAA;YAAAkE,QAAA,EAAOjC,aAAa,CAAC;UAAC;YAAAqC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAO,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC3B,CAAC,eAGNzE,OAAA;UAAKmE,KAAK,EAAE;YAAER,YAAY,EAAE,MAAM;YAAEZ,QAAQ,EAAE,MAAM;YAAE2B,OAAO,EAAE;UAAI,CAAE;UAAAR,QAAA,gBACnElE,OAAA;YAAAkE,QAAA,GAAK,kBAAgB,EAACpE,iBAAiB,IAAI,SAAS;UAAA;YAAAwE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC3DzE,OAAA;YAAAkE,QAAA,GAAK,eAAa,EAACS,OAAO,CAACC,GAAG,CAACC,QAAQ;UAAA;YAAAP,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAC9CzE,OAAA;YAAAkE,QAAA,GAAK,cAAY,EAACrE,UAAU,GAAG,IAAI,GAAG,KAAK;UAAA;YAAAyE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDzE,OAAA;YAAAkE,QAAA,GAAK,iBAAe,EAACtE,gBAAgB,CAACkF,mBAAmB,GAAG,IAAI,GAAG,KAAK;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5E,CAAC,eAGNzE,OAAA;UAAAkE,QAAA,gBACElE,OAAA;YAAKmE,KAAK,EAAE;cAAER,YAAY,EAAE,KAAK;cAAEoB,UAAU,EAAE;YAAO,CAAE;YAAAb,QAAA,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,EAC5EjE,MAAM,CAACwE,MAAM,KAAK,CAAC,gBAClBhF,OAAA;YAAKmE,KAAK,EAAE;cAAEO,OAAO,EAAE,GAAG;cAAEO,SAAS,EAAE;YAAS,CAAE;YAAAf,QAAA,EAAC;UAEnD;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,GAENjE,MAAM,CAAC0E,GAAG,CAAC,CAACC,KAAK,EAAEC,KAAK,kBACtBpF,OAAA;YAAiBmE,KAAK,EAAEL,WAAY;YAAAI,QAAA,gBAClClE,OAAA;cAAKmE,KAAK,EAAE;gBAAEhB,OAAO,EAAE,MAAM;gBAAEC,cAAc,EAAE,eAAe;gBAAEO,YAAY,EAAE;cAAM,CAAE;cAAAO,QAAA,gBACpFlE,OAAA;gBAAMmE,KAAK,EAAE;kBAAEY,UAAU,EAAE,MAAM;kBAAEtC,KAAK,EAAE;gBAAU,CAAE;gBAAAyB,QAAA,EACnDiB,KAAK,CAACxD;cAAI;gBAAA2C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACP,CAAC,eACPzE,OAAA;gBAAMmE,KAAK,EAAE;kBAAEO,OAAO,EAAE;gBAAI,CAAE;gBAAAR,QAAA,EAAEiB,KAAK,CAAC5D;cAAS;gBAAA+C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpD,CAAC,eACNzE,OAAA;cAAKmE,KAAK,EAAE;gBAAEpB,QAAQ,EAAE,MAAM;gBAAE2B,OAAO,EAAE;cAAI,CAAE;cAAAR,QAAA,EAC5CmB,MAAM,CAACC,IAAI,CAACH,KAAK,CAACvD,IAAI,CAAC,CAACoD,MAAM,GAAG,CAAC,GACjCK,MAAM,CAACE,OAAO,CAACJ,KAAK,CAACvD,IAAI,CAAC,CAACC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAACqD,GAAG,CAAC,CAAC,CAACM,GAAG,EAAEC,KAAK,CAAC,kBACtDzF,OAAA;gBAAAkE,QAAA,GACGsB,GAAG,EAAC,IAAE,EAAC,OAAOC,KAAK,KAAK,QAAQ,GAAGC,IAAI,CAACC,SAAS,CAACF,KAAK,CAAC,CAAC5D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,KAAK,GAAG+D,MAAM,CAACH,KAAK,CAAC,CAAC5D,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;cAAA,GADnG2D,GAAG;gBAAAlB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAER,CACN,CAAC,GAEF;YACD;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA,GAjBEW,KAAK;YAAAd,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAkBV,CACN,CACF;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNzE,OAAA;UAAKmE,KAAK,EAAE;YAAE0B,SAAS,EAAE,MAAM;YAAEC,UAAU,EAAE,MAAM;YAAEC,SAAS,EAAE;UAAqC,CAAE;UAAA7B,QAAA,gBACrGlE,OAAA;YAAKmE,KAAK,EAAE;cAAER,YAAY,EAAE,KAAK;cAAEoB,UAAU,EAAE;YAAO,CAAE;YAAAb,QAAA,EAAC;UAAc;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAK,CAAC,eAC7EzE,OAAA;YACEmE,KAAK,EAAE;cACL3B,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,OAAO;cACdwB,MAAM,EAAE,MAAM;cACdhB,OAAO,EAAE,SAAS;cAClBP,YAAY,EAAE,KAAK;cACnBY,MAAM,EAAE,SAAS;cACjB0C,WAAW,EAAE,KAAK;cAClBjD,QAAQ,EAAE;YACZ,CAAE;YACFqB,OAAO,EAAEA,CAAA,KAAM;cACb,IAAIvD,MAAM,CAACC,IAAI,EAAE;gBACfD,MAAM,CAACC,IAAI,CAAC,OAAO,EAAE,YAAY,EAAE;kBACjCmF,cAAc,EAAE,OAAO;kBACvBC,WAAW,EAAE;gBACf,CAAC,CAAC;cACJ;YACF,CAAE;YAAAhC,QAAA,EACH;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eACTzE,OAAA;YACEmE,KAAK,EAAE;cACL3B,eAAe,EAAE,SAAS;cAC1BC,KAAK,EAAE,OAAO;cACdwB,MAAM,EAAE,MAAM;cACdhB,OAAO,EAAE,SAAS;cAClBP,YAAY,EAAE,KAAK;cACnBY,MAAM,EAAE,SAAS;cACjBP,QAAQ,EAAE;YACZ,CAAE;YACFqB,OAAO,EAAEA,CAAA,KAAM3D,SAAS,CAAC,EAAE,CAAE;YAAAyD,QAAA,EAC9B;UAED;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACE,CAAC;EAAA,eACN,CAAC;AAEP,CAAC;AAACpE,EAAA,CAhQIF,iBAAiB;AAAAgG,EAAA,GAAjBhG,iBAAiB;AAkQvB,eAAeA,iBAAiB;AAAC,IAAAgG,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}