{"ast": null, "code": "/**\n * Google Analytics 4 Service for Kubera Horoscope Website\n * Comprehensive tracking for page views, events, ecommerce, and user interactions\n */\n\nimport { ANALYTICS_CONFIG, EVENT_CATEGORIES, CUSTOM_EVENTS, PRODUCTS, DEBUG_MODE } from '../config/analytics';\n\n// Check if Google Analytics is loaded and available\nconst isGALoaded = () => {\n  return typeof window !== 'undefined' && typeof window.gtag === 'function';\n};\n\n// Get the GA Measurement ID from configuration\nconst GA_MEASUREMENT_ID = ANALYTICS_CONFIG.measurementId;\n\n// Logging utility\nconst logEvent = (message, data = null) => {\n  if (ANALYTICS_CONFIG.enableLogging || DEBUG_MODE) {\n    console.log(`[Analytics] ${message}`, data || '');\n  }\n};\n\n/**\n * Initialize Google Analytics\n */\nexport const initGA = () => {\n  // Skip initialization in development if disabled\n  if (process.env.NODE_ENV === 'development' && !ANALYTICS_CONFIG.enableInDevelopment) {\n    logEvent('Analytics disabled in development mode');\n    return false;\n  }\n  if (!GA_MEASUREMENT_ID || GA_MEASUREMENT_ID === 'G-XXXXXXXXXX') {\n    console.warn('Google Analytics Measurement ID not configured. Please set REACT_APP_GA_MEASUREMENT_ID in your .env file.');\n    return false;\n  }\n\n  // Use the window.initializeGA function if available\n  if (typeof window !== 'undefined' && typeof window.initializeGA === 'function') {\n    window.initializeGA(GA_MEASUREMENT_ID);\n    logEvent('Google Analytics initialized', GA_MEASUREMENT_ID);\n    return true;\n  }\n  if (isGALoaded()) {\n    logEvent('Google Analytics already loaded', GA_MEASUREMENT_ID);\n    return true;\n  }\n  console.warn('Google Analytics initialization function not found. Make sure the script is included in index.html');\n  return false;\n};\n\n/**\n * Track page views\n * @param {string} path - The page path\n * @param {string} title - The page title\n * @param {Object} additionalParams - Additional parameters\n */\nexport const trackPageView = (path, title = '', additionalParams = {}) => {\n  if (!isGALoaded()) return;\n  const params = {\n    page_title: title,\n    page_location: `${window.location.origin}${path}`,\n    page_path: path,\n    ...additionalParams\n  };\n  window.gtag('config', GA_MEASUREMENT_ID, params);\n\n  // Also send as a page_view event for better tracking\n  window.gtag('event', 'page_view', {\n    page_title: title,\n    page_location: `${window.location.origin}${path}`,\n    page_path: path,\n    ...additionalParams\n  });\n  console.log('Page view tracked:', path, title);\n};\n\n/**\n * Track custom events\n * @param {string} eventName - Name of the event\n * @param {Object} parameters - Event parameters\n */\nexport const trackEvent = (eventName, parameters = {}) => {\n  if (!isGALoaded()) {\n    logEvent(`Event not tracked (GA not loaded): ${eventName}`, parameters);\n    return;\n  }\n  const eventData = {\n    event_category: parameters.category || EVENT_CATEGORIES.USER_ENGAGEMENT,\n    event_label: parameters.label || '',\n    value: parameters.value || 0,\n    ...parameters\n  };\n  window.gtag('event', eventName, eventData);\n  logEvent(`Event tracked: ${eventName}`, eventData);\n};\n\n/**\n * Track zodiac sign interactions\n * @param {string} zodiacSign - The zodiac sign (e.g., 'aries', 'taurus')\n * @param {string} action - The action taken (e.g., 'view', 'click')\n * @param {Object} additionalParams - Additional parameters\n */\nexport const trackZodiacInteraction = (zodiacSign, action = 'view', additionalParams = {}) => {\n  trackEvent('zodiac_interaction', {\n    category: 'zodiac',\n    label: zodiacSign,\n    zodiac_sign: zodiacSign,\n    interaction_type: action,\n    ...additionalParams\n  });\n};\n\n/**\n * Track Kubera Card product interactions\n * @param {string} action - The action (view_item, add_to_cart, purchase, etc.)\n * @param {Object} productData - Product information\n */\nexport const trackKuberaCardEvent = (action, productData = {}) => {\n  if (!isGALoaded()) {\n    logEvent(`Kubera Card event not tracked (GA not loaded): ${action}`, productData);\n    return;\n  }\n  const product = PRODUCTS.KUBERA_CARD;\n  const defaultProductData = {\n    ...product,\n    price: productData.price || product.price,\n    quantity: productData.quantity || 1\n  };\n  const eventData = {\n    currency: ANALYTICS_CONFIG.defaultCurrency,\n    value: defaultProductData.price * defaultProductData.quantity,\n    items: [{\n      ...defaultProductData,\n      ...productData\n    }]\n  };\n  window.gtag('event', action, eventData);\n  logEvent(`Kubera Card event tracked: ${action}`, eventData);\n};\n\n/**\n * Track ecommerce purchase\n * @param {Object} purchaseData - Purchase information\n */\nexport const trackPurchase = purchaseData => {\n  if (!isGALoaded()) return;\n  const {\n    transaction_id,\n    value,\n    currency = 'LKR',\n    items = [],\n    shipping = 0,\n    tax = 0\n  } = purchaseData;\n  window.gtag('event', 'purchase', {\n    transaction_id,\n    value,\n    currency,\n    items,\n    shipping,\n    tax\n  });\n  console.log('Purchase tracked:', purchaseData);\n};\n\n/**\n * Track form submissions\n * @param {string} formName - Name of the form\n * @param {Object} formData - Form data (be careful with PII)\n */\nexport const trackFormSubmission = (formName, formData = {}) => {\n  trackEvent('form_submit', {\n    category: 'form',\n    label: formName,\n    form_name: formName,\n    ...formData\n  });\n};\n\n/**\n * Track user engagement time\n * @param {number} engagementTime - Time in seconds\n * @param {string} page - Page identifier\n */\nexport const trackEngagement = (engagementTime, page = '') => {\n  trackEvent('user_engagement', {\n    category: 'engagement',\n    label: page,\n    value: Math.round(engagementTime),\n    engagement_time_msec: engagementTime * 1000\n  });\n};\n\n/**\n * Track scroll depth\n * @param {number} scrollPercentage - Percentage scrolled (0-100)\n * @param {string} page - Page identifier\n */\nexport const trackScrollDepth = (scrollPercentage, page = '') => {\n  // Only track at certain milestones\n  const milestones = [25, 50, 75, 90, 100];\n  if (milestones.includes(scrollPercentage)) {\n    trackEvent('scroll', {\n      category: 'engagement',\n      label: `${scrollPercentage}%`,\n      value: scrollPercentage,\n      page_path: page\n    });\n  }\n};\n\n/**\n * Track search events\n * @param {string} searchTerm - What the user searched for\n * @param {number} resultsCount - Number of results returned\n */\nexport const trackSearch = (searchTerm, resultsCount = 0) => {\n  trackEvent('search', {\n    category: 'search',\n    label: searchTerm,\n    search_term: searchTerm,\n    results_count: resultsCount\n  });\n};\n\n/**\n * Track language preference\n * @param {string} language - Language code (e.g., 'si', 'en')\n */\nexport const trackLanguagePreference = language => {\n  trackEvent('language_preference', {\n    category: 'user_preference',\n    label: language,\n    language: language\n  });\n};\n\n/**\n * Track errors\n * @param {string} errorMessage - Error message\n * @param {string} errorLocation - Where the error occurred\n * @param {boolean} fatal - Whether the error was fatal\n */\nexport const trackError = (errorMessage, errorLocation = '', fatal = false) => {\n  trackEvent('exception', {\n    description: errorMessage,\n    fatal: fatal,\n    error_location: errorLocation\n  });\n};\n\n/**\n * Track timing events (e.g., page load time, API response time)\n * @param {string} name - Name of the timing event\n * @param {number} value - Time in milliseconds\n * @param {string} category - Category of the timing event\n */\nexport const trackTiming = (name, value, category = 'performance') => {\n  trackEvent('timing_complete', {\n    name: name,\n    value: Math.round(value),\n    event_category: category\n  });\n};\n\n/**\n * Set user properties\n * @param {Object} properties - User properties to set\n */\nexport const setUserProperties = properties => {\n  if (!isGALoaded()) return;\n  window.gtag('config', GA_MEASUREMENT_ID, {\n    user_properties: properties\n  });\n  console.log('User properties set:', properties);\n};\n\n/**\n * Track outbound links\n * @param {string} url - The external URL\n * @param {string} linkText - Text of the link\n */\nexport const trackOutboundLink = (url, linkText = '') => {\n  trackEvent('click', {\n    event_category: 'outbound',\n    event_label: url,\n    link_text: linkText,\n    link_url: url\n  });\n};\n\n// Export the GA Measurement ID for use in other components\nexport { GA_MEASUREMENT_ID };", "map": {"version": 3, "names": ["ANALYTICS_CONFIG", "EVENT_CATEGORIES", "CUSTOM_EVENTS", "PRODUCTS", "DEBUG_MODE", "isGALoaded", "window", "gtag", "GA_MEASUREMENT_ID", "measurementId", "logEvent", "message", "data", "enableLogging", "console", "log", "initGA", "process", "env", "NODE_ENV", "enableInDevelopment", "warn", "initializeGA", "trackPageView", "path", "title", "additionalParams", "params", "page_title", "page_location", "location", "origin", "page_path", "trackEvent", "eventName", "parameters", "eventData", "event_category", "category", "USER_ENGAGEMENT", "event_label", "label", "value", "trackZodiacInteraction", "zodiacSign", "action", "zodiac_sign", "interaction_type", "trackKuberaCardEvent", "productData", "product", "KUBERA_CARD", "defaultProductData", "price", "quantity", "currency", "defaultCurrency", "items", "trackPurchase", "purchaseData", "transaction_id", "shipping", "tax", "trackFormSubmission", "formName", "formData", "form_name", "trackEngagement", "engagementTime", "page", "Math", "round", "engagement_time_msec", "trackScrollDepth", "scrollPercentage", "milestones", "includes", "trackSearch", "searchTerm", "resultsCount", "search_term", "results_count", "trackLanguagePreference", "language", "trackError", "errorMessage", "errorLocation", "fatal", "description", "error_location", "trackTiming", "name", "setUserProperties", "properties", "user_properties", "trackOutboundLink", "url", "linkText", "link_text", "link_url"], "sources": ["C:/Users/<USER>/Desktop/Horoscope/src/services/analytics.js"], "sourcesContent": ["/**\n * Google Analytics 4 Service for Kubera Horoscope Website\n * Comprehensive tracking for page views, events, ecommerce, and user interactions\n */\n\nimport {\n  ANALYTICS_CONFIG,\n  EVENT_CATEGORIES,\n  CUSTOM_EVENTS,\n  PRODUCTS,\n  DEBUG_MODE\n} from '../config/analytics';\n\n// Check if Google Analytics is loaded and available\nconst isGALoaded = () => {\n  return typeof window !== 'undefined' && typeof window.gtag === 'function';\n};\n\n// Get the GA Measurement ID from configuration\nconst GA_MEASUREMENT_ID = ANALYTICS_CONFIG.measurementId;\n\n// Logging utility\nconst logEvent = (message, data = null) => {\n  if (ANALYTICS_CONFIG.enableLogging || DEBUG_MODE) {\n    console.log(`[Analytics] ${message}`, data || '');\n  }\n};\n\n/**\n * Initialize Google Analytics\n */\nexport const initGA = () => {\n  // Skip initialization in development if disabled\n  if (process.env.NODE_ENV === 'development' && !ANALYTICS_CONFIG.enableInDevelopment) {\n    logEvent('Analytics disabled in development mode');\n    return false;\n  }\n\n  if (!GA_MEASUREMENT_ID || GA_MEASUREMENT_ID === 'G-XXXXXXXXXX') {\n    console.warn('Google Analytics Measurement ID not configured. Please set REACT_APP_GA_MEASUREMENT_ID in your .env file.');\n    return false;\n  }\n\n  // Use the window.initializeGA function if available\n  if (typeof window !== 'undefined' && typeof window.initializeGA === 'function') {\n    window.initializeGA(GA_MEASUREMENT_ID);\n    logEvent('Google Analytics initialized', GA_MEASUREMENT_ID);\n    return true;\n  }\n\n  if (isGALoaded()) {\n    logEvent('Google Analytics already loaded', GA_MEASUREMENT_ID);\n    return true;\n  }\n\n  console.warn('Google Analytics initialization function not found. Make sure the script is included in index.html');\n  return false;\n};\n\n/**\n * Track page views\n * @param {string} path - The page path\n * @param {string} title - The page title\n * @param {Object} additionalParams - Additional parameters\n */\nexport const trackPageView = (path, title = '', additionalParams = {}) => {\n  if (!isGALoaded()) return;\n\n  const params = {\n    page_title: title,\n    page_location: `${window.location.origin}${path}`,\n    page_path: path,\n    ...additionalParams\n  };\n\n  window.gtag('config', GA_MEASUREMENT_ID, params);\n  \n  // Also send as a page_view event for better tracking\n  window.gtag('event', 'page_view', {\n    page_title: title,\n    page_location: `${window.location.origin}${path}`,\n    page_path: path,\n    ...additionalParams\n  });\n\n  console.log('Page view tracked:', path, title);\n};\n\n/**\n * Track custom events\n * @param {string} eventName - Name of the event\n * @param {Object} parameters - Event parameters\n */\nexport const trackEvent = (eventName, parameters = {}) => {\n  if (!isGALoaded()) {\n    logEvent(`Event not tracked (GA not loaded): ${eventName}`, parameters);\n    return;\n  }\n\n  const eventData = {\n    event_category: parameters.category || EVENT_CATEGORIES.USER_ENGAGEMENT,\n    event_label: parameters.label || '',\n    value: parameters.value || 0,\n    ...parameters\n  };\n\n  window.gtag('event', eventName, eventData);\n  logEvent(`Event tracked: ${eventName}`, eventData);\n};\n\n/**\n * Track zodiac sign interactions\n * @param {string} zodiacSign - The zodiac sign (e.g., 'aries', 'taurus')\n * @param {string} action - The action taken (e.g., 'view', 'click')\n * @param {Object} additionalParams - Additional parameters\n */\nexport const trackZodiacInteraction = (zodiacSign, action = 'view', additionalParams = {}) => {\n  trackEvent('zodiac_interaction', {\n    category: 'zodiac',\n    label: zodiacSign,\n    zodiac_sign: zodiacSign,\n    interaction_type: action,\n    ...additionalParams\n  });\n};\n\n/**\n * Track Kubera Card product interactions\n * @param {string} action - The action (view_item, add_to_cart, purchase, etc.)\n * @param {Object} productData - Product information\n */\nexport const trackKuberaCardEvent = (action, productData = {}) => {\n  if (!isGALoaded()) {\n    logEvent(`Kubera Card event not tracked (GA not loaded): ${action}`, productData);\n    return;\n  }\n\n  const product = PRODUCTS.KUBERA_CARD;\n  const defaultProductData = {\n    ...product,\n    price: productData.price || product.price,\n    quantity: productData.quantity || 1\n  };\n\n  const eventData = {\n    currency: ANALYTICS_CONFIG.defaultCurrency,\n    value: defaultProductData.price * defaultProductData.quantity,\n    items: [{ ...defaultProductData, ...productData }]\n  };\n\n  window.gtag('event', action, eventData);\n  logEvent(`Kubera Card event tracked: ${action}`, eventData);\n};\n\n/**\n * Track ecommerce purchase\n * @param {Object} purchaseData - Purchase information\n */\nexport const trackPurchase = (purchaseData) => {\n  if (!isGALoaded()) return;\n\n  const {\n    transaction_id,\n    value,\n    currency = 'LKR',\n    items = [],\n    shipping = 0,\n    tax = 0\n  } = purchaseData;\n\n  window.gtag('event', 'purchase', {\n    transaction_id,\n    value,\n    currency,\n    items,\n    shipping,\n    tax\n  });\n\n  console.log('Purchase tracked:', purchaseData);\n};\n\n/**\n * Track form submissions\n * @param {string} formName - Name of the form\n * @param {Object} formData - Form data (be careful with PII)\n */\nexport const trackFormSubmission = (formName, formData = {}) => {\n  trackEvent('form_submit', {\n    category: 'form',\n    label: formName,\n    form_name: formName,\n    ...formData\n  });\n};\n\n/**\n * Track user engagement time\n * @param {number} engagementTime - Time in seconds\n * @param {string} page - Page identifier\n */\nexport const trackEngagement = (engagementTime, page = '') => {\n  trackEvent('user_engagement', {\n    category: 'engagement',\n    label: page,\n    value: Math.round(engagementTime),\n    engagement_time_msec: engagementTime * 1000\n  });\n};\n\n/**\n * Track scroll depth\n * @param {number} scrollPercentage - Percentage scrolled (0-100)\n * @param {string} page - Page identifier\n */\nexport const trackScrollDepth = (scrollPercentage, page = '') => {\n  // Only track at certain milestones\n  const milestones = [25, 50, 75, 90, 100];\n  if (milestones.includes(scrollPercentage)) {\n    trackEvent('scroll', {\n      category: 'engagement',\n      label: `${scrollPercentage}%`,\n      value: scrollPercentage,\n      page_path: page\n    });\n  }\n};\n\n/**\n * Track search events\n * @param {string} searchTerm - What the user searched for\n * @param {number} resultsCount - Number of results returned\n */\nexport const trackSearch = (searchTerm, resultsCount = 0) => {\n  trackEvent('search', {\n    category: 'search',\n    label: searchTerm,\n    search_term: searchTerm,\n    results_count: resultsCount\n  });\n};\n\n/**\n * Track language preference\n * @param {string} language - Language code (e.g., 'si', 'en')\n */\nexport const trackLanguagePreference = (language) => {\n  trackEvent('language_preference', {\n    category: 'user_preference',\n    label: language,\n    language: language\n  });\n};\n\n/**\n * Track errors\n * @param {string} errorMessage - Error message\n * @param {string} errorLocation - Where the error occurred\n * @param {boolean} fatal - Whether the error was fatal\n */\nexport const trackError = (errorMessage, errorLocation = '', fatal = false) => {\n  trackEvent('exception', {\n    description: errorMessage,\n    fatal: fatal,\n    error_location: errorLocation\n  });\n};\n\n/**\n * Track timing events (e.g., page load time, API response time)\n * @param {string} name - Name of the timing event\n * @param {number} value - Time in milliseconds\n * @param {string} category - Category of the timing event\n */\nexport const trackTiming = (name, value, category = 'performance') => {\n  trackEvent('timing_complete', {\n    name: name,\n    value: Math.round(value),\n    event_category: category\n  });\n};\n\n/**\n * Set user properties\n * @param {Object} properties - User properties to set\n */\nexport const setUserProperties = (properties) => {\n  if (!isGALoaded()) return;\n\n  window.gtag('config', GA_MEASUREMENT_ID, {\n    user_properties: properties\n  });\n\n  console.log('User properties set:', properties);\n};\n\n/**\n * Track outbound links\n * @param {string} url - The external URL\n * @param {string} linkText - Text of the link\n */\nexport const trackOutboundLink = (url, linkText = '') => {\n  trackEvent('click', {\n    event_category: 'outbound',\n    event_label: url,\n    link_text: linkText,\n    link_url: url\n  });\n};\n\n// Export the GA Measurement ID for use in other components\nexport { GA_MEASUREMENT_ID };\n"], "mappings": "AAAA;AACA;AACA;AACA;;AAEA,SACEA,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,QAAQ,EACRC,UAAU,QACL,qBAAqB;;AAE5B;AACA,MAAMC,UAAU,GAAGA,CAAA,KAAM;EACvB,OAAO,OAAOC,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACC,IAAI,KAAK,UAAU;AAC3E,CAAC;;AAED;AACA,MAAMC,iBAAiB,GAAGR,gBAAgB,CAACS,aAAa;;AAExD;AACA,MAAMC,QAAQ,GAAGA,CAACC,OAAO,EAAEC,IAAI,GAAG,IAAI,KAAK;EACzC,IAAIZ,gBAAgB,CAACa,aAAa,IAAIT,UAAU,EAAE;IAChDU,OAAO,CAACC,GAAG,CAAC,eAAeJ,OAAO,EAAE,EAAEC,IAAI,IAAI,EAAE,CAAC;EACnD;AACF,CAAC;;AAED;AACA;AACA;AACA,OAAO,MAAMI,MAAM,GAAGA,CAAA,KAAM;EAC1B;EACA,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,IAAI,CAACnB,gBAAgB,CAACoB,mBAAmB,EAAE;IACnFV,QAAQ,CAAC,wCAAwC,CAAC;IAClD,OAAO,KAAK;EACd;EAEA,IAAI,CAACF,iBAAiB,IAAIA,iBAAiB,KAAK,cAAc,EAAE;IAC9DM,OAAO,CAACO,IAAI,CAAC,2GAA2G,CAAC;IACzH,OAAO,KAAK;EACd;;EAEA;EACA,IAAI,OAAOf,MAAM,KAAK,WAAW,IAAI,OAAOA,MAAM,CAACgB,YAAY,KAAK,UAAU,EAAE;IAC9EhB,MAAM,CAACgB,YAAY,CAACd,iBAAiB,CAAC;IACtCE,QAAQ,CAAC,8BAA8B,EAAEF,iBAAiB,CAAC;IAC3D,OAAO,IAAI;EACb;EAEA,IAAIH,UAAU,CAAC,CAAC,EAAE;IAChBK,QAAQ,CAAC,iCAAiC,EAAEF,iBAAiB,CAAC;IAC9D,OAAO,IAAI;EACb;EAEAM,OAAO,CAACO,IAAI,CAAC,oGAAoG,CAAC;EAClH,OAAO,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,aAAa,GAAGA,CAACC,IAAI,EAAEC,KAAK,GAAG,EAAE,EAAEC,gBAAgB,GAAG,CAAC,CAAC,KAAK;EACxE,IAAI,CAACrB,UAAU,CAAC,CAAC,EAAE;EAEnB,MAAMsB,MAAM,GAAG;IACbC,UAAU,EAAEH,KAAK;IACjBI,aAAa,EAAE,GAAGvB,MAAM,CAACwB,QAAQ,CAACC,MAAM,GAAGP,IAAI,EAAE;IACjDQ,SAAS,EAAER,IAAI;IACf,GAAGE;EACL,CAAC;EAEDpB,MAAM,CAACC,IAAI,CAAC,QAAQ,EAAEC,iBAAiB,EAAEmB,MAAM,CAAC;;EAEhD;EACArB,MAAM,CAACC,IAAI,CAAC,OAAO,EAAE,WAAW,EAAE;IAChCqB,UAAU,EAAEH,KAAK;IACjBI,aAAa,EAAE,GAAGvB,MAAM,CAACwB,QAAQ,CAACC,MAAM,GAAGP,IAAI,EAAE;IACjDQ,SAAS,EAAER,IAAI;IACf,GAAGE;EACL,CAAC,CAAC;EAEFZ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAES,IAAI,EAAEC,KAAK,CAAC;AAChD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,UAAU,GAAGA,CAACC,SAAS,EAAEC,UAAU,GAAG,CAAC,CAAC,KAAK;EACxD,IAAI,CAAC9B,UAAU,CAAC,CAAC,EAAE;IACjBK,QAAQ,CAAC,sCAAsCwB,SAAS,EAAE,EAAEC,UAAU,CAAC;IACvE;EACF;EAEA,MAAMC,SAAS,GAAG;IAChBC,cAAc,EAAEF,UAAU,CAACG,QAAQ,IAAIrC,gBAAgB,CAACsC,eAAe;IACvEC,WAAW,EAAEL,UAAU,CAACM,KAAK,IAAI,EAAE;IACnCC,KAAK,EAAEP,UAAU,CAACO,KAAK,IAAI,CAAC;IAC5B,GAAGP;EACL,CAAC;EAED7B,MAAM,CAACC,IAAI,CAAC,OAAO,EAAE2B,SAAS,EAAEE,SAAS,CAAC;EAC1C1B,QAAQ,CAAC,kBAAkBwB,SAAS,EAAE,EAAEE,SAAS,CAAC;AACpD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMO,sBAAsB,GAAGA,CAACC,UAAU,EAAEC,MAAM,GAAG,MAAM,EAAEnB,gBAAgB,GAAG,CAAC,CAAC,KAAK;EAC5FO,UAAU,CAAC,oBAAoB,EAAE;IAC/BK,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAEG,UAAU;IACjBE,WAAW,EAAEF,UAAU;IACvBG,gBAAgB,EAAEF,MAAM;IACxB,GAAGnB;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsB,oBAAoB,GAAGA,CAACH,MAAM,EAAEI,WAAW,GAAG,CAAC,CAAC,KAAK;EAChE,IAAI,CAAC5C,UAAU,CAAC,CAAC,EAAE;IACjBK,QAAQ,CAAC,kDAAkDmC,MAAM,EAAE,EAAEI,WAAW,CAAC;IACjF;EACF;EAEA,MAAMC,OAAO,GAAG/C,QAAQ,CAACgD,WAAW;EACpC,MAAMC,kBAAkB,GAAG;IACzB,GAAGF,OAAO;IACVG,KAAK,EAAEJ,WAAW,CAACI,KAAK,IAAIH,OAAO,CAACG,KAAK;IACzCC,QAAQ,EAAEL,WAAW,CAACK,QAAQ,IAAI;EACpC,CAAC;EAED,MAAMlB,SAAS,GAAG;IAChBmB,QAAQ,EAAEvD,gBAAgB,CAACwD,eAAe;IAC1Cd,KAAK,EAAEU,kBAAkB,CAACC,KAAK,GAAGD,kBAAkB,CAACE,QAAQ;IAC7DG,KAAK,EAAE,CAAC;MAAE,GAAGL,kBAAkB;MAAE,GAAGH;IAAY,CAAC;EACnD,CAAC;EAED3C,MAAM,CAACC,IAAI,CAAC,OAAO,EAAEsC,MAAM,EAAET,SAAS,CAAC;EACvC1B,QAAQ,CAAC,8BAA8BmC,MAAM,EAAE,EAAET,SAAS,CAAC;AAC7D,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMsB,aAAa,GAAIC,YAAY,IAAK;EAC7C,IAAI,CAACtD,UAAU,CAAC,CAAC,EAAE;EAEnB,MAAM;IACJuD,cAAc;IACdlB,KAAK;IACLa,QAAQ,GAAG,KAAK;IAChBE,KAAK,GAAG,EAAE;IACVI,QAAQ,GAAG,CAAC;IACZC,GAAG,GAAG;EACR,CAAC,GAAGH,YAAY;EAEhBrD,MAAM,CAACC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE;IAC/BqD,cAAc;IACdlB,KAAK;IACLa,QAAQ;IACRE,KAAK;IACLI,QAAQ;IACRC;EACF,CAAC,CAAC;EAEFhD,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE4C,YAAY,CAAC;AAChD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,mBAAmB,GAAGA,CAACC,QAAQ,EAAEC,QAAQ,GAAG,CAAC,CAAC,KAAK;EAC9DhC,UAAU,CAAC,aAAa,EAAE;IACxBK,QAAQ,EAAE,MAAM;IAChBG,KAAK,EAAEuB,QAAQ;IACfE,SAAS,EAAEF,QAAQ;IACnB,GAAGC;EACL,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,eAAe,GAAGA,CAACC,cAAc,EAAEC,IAAI,GAAG,EAAE,KAAK;EAC5DpC,UAAU,CAAC,iBAAiB,EAAE;IAC5BK,QAAQ,EAAE,YAAY;IACtBG,KAAK,EAAE4B,IAAI;IACX3B,KAAK,EAAE4B,IAAI,CAACC,KAAK,CAACH,cAAc,CAAC;IACjCI,oBAAoB,EAAEJ,cAAc,GAAG;EACzC,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMK,gBAAgB,GAAGA,CAACC,gBAAgB,EAAEL,IAAI,GAAG,EAAE,KAAK;EAC/D;EACA,MAAMM,UAAU,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG,CAAC;EACxC,IAAIA,UAAU,CAACC,QAAQ,CAACF,gBAAgB,CAAC,EAAE;IACzCzC,UAAU,CAAC,QAAQ,EAAE;MACnBK,QAAQ,EAAE,YAAY;MACtBG,KAAK,EAAE,GAAGiC,gBAAgB,GAAG;MAC7BhC,KAAK,EAAEgC,gBAAgB;MACvB1C,SAAS,EAAEqC;IACb,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMQ,WAAW,GAAGA,CAACC,UAAU,EAAEC,YAAY,GAAG,CAAC,KAAK;EAC3D9C,UAAU,CAAC,QAAQ,EAAE;IACnBK,QAAQ,EAAE,QAAQ;IAClBG,KAAK,EAAEqC,UAAU;IACjBE,WAAW,EAAEF,UAAU;IACvBG,aAAa,EAAEF;EACjB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMG,uBAAuB,GAAIC,QAAQ,IAAK;EACnDlD,UAAU,CAAC,qBAAqB,EAAE;IAChCK,QAAQ,EAAE,iBAAiB;IAC3BG,KAAK,EAAE0C,QAAQ;IACfA,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,UAAU,GAAGA,CAACC,YAAY,EAAEC,aAAa,GAAG,EAAE,EAAEC,KAAK,GAAG,KAAK,KAAK;EAC7EtD,UAAU,CAAC,WAAW,EAAE;IACtBuD,WAAW,EAAEH,YAAY;IACzBE,KAAK,EAAEA,KAAK;IACZE,cAAc,EAAEH;EAClB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMI,WAAW,GAAGA,CAACC,IAAI,EAAEjD,KAAK,EAAEJ,QAAQ,GAAG,aAAa,KAAK;EACpEL,UAAU,CAAC,iBAAiB,EAAE;IAC5B0D,IAAI,EAAEA,IAAI;IACVjD,KAAK,EAAE4B,IAAI,CAACC,KAAK,CAAC7B,KAAK,CAAC;IACxBL,cAAc,EAAEC;EAClB,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA,OAAO,MAAMsD,iBAAiB,GAAIC,UAAU,IAAK;EAC/C,IAAI,CAACxF,UAAU,CAAC,CAAC,EAAE;EAEnBC,MAAM,CAACC,IAAI,CAAC,QAAQ,EAAEC,iBAAiB,EAAE;IACvCsF,eAAe,EAAED;EACnB,CAAC,CAAC;EAEF/E,OAAO,CAACC,GAAG,CAAC,sBAAsB,EAAE8E,UAAU,CAAC;AACjD,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,OAAO,MAAME,iBAAiB,GAAGA,CAACC,GAAG,EAAEC,QAAQ,GAAG,EAAE,KAAK;EACvDhE,UAAU,CAAC,OAAO,EAAE;IAClBI,cAAc,EAAE,UAAU;IAC1BG,WAAW,EAAEwD,GAAG;IAChBE,SAAS,EAAED,QAAQ;IACnBE,QAAQ,EAAEH;EACZ,CAAC,CAAC;AACJ,CAAC;;AAED;AACA,SAASxF,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}