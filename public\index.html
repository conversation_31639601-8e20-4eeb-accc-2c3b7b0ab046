<!DOCTYPE html>
<html lang="si">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="%PUBLIC_URL%/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="theme-color" content="#000000" />
    <meta name="description" content="කුබේර දෙවියන්ගේ ආශීර්වාදය - දෛනික රාශිඵල" />
    <meta name="keywords" content="සිංහල, ජ්‍යොතිෂය, රාශිඵල, දෛනික, කුබේර දෙවියන්" />
    <meta property="og:title" content="කුබේර දෙවියන්ගේ ආශීර්වාදය" />
    <meta property="og:description" content="දෛනික රාශිඵල සිංහල භාෂාවෙන්" />
    <meta property="og:type" content="website" />
    <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
    <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
    
    <!-- Google Analytics 4 -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=%REACT_APP_GA_MEASUREMENT_ID%"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '%REACT_APP_GA_MEASUREMENT_ID%', {
        page_title: 'කුබේර දෙවියන්ගේ ආශීර්වාදය - දෛනික රාශිඵල',
        page_location: window.location.href,
        send_page_view: true,
        custom_map: {
          'custom_parameter_1': 'zodiac_sign',
          'custom_parameter_2': 'user_language'
        }
      });

      // Enhanced ecommerce setup
      gtag('config', '%REACT_APP_GA_MEASUREMENT_ID%', {
        custom_map: {
          'custom_parameter_3': 'product_category',
          'custom_parameter_4': 'checkout_step'
        }
      });
    </script>

    <!-- Sinhala Font Loading -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Sinhala:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <title>කුබේර දෙවියන්ගේ ආශීර්වාදය - දෛනික රාශිඵල</title>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root"></div>
  </body>
</html>