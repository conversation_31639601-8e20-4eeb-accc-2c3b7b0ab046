import React, { useState, useEffect } from 'react';
import KuberaCheckout from './KuberaCheckout';
import { useAnalytics, useComponentTracking } from '../hooks/useAnalytics';

const KuberaCardSection = () => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showCheckout, setShowCheckout] = useState(false);

  // Analytics integration
  const analytics = useAnalytics();
  useComponentTracking('KuberaCardSection');

  // Kubera Card product images - using custom PNG designs
  const productImages = [
    '/images/kubera-card-1.png',
    '/images/kubera-card-2.png',
    '/images/kubera-card-3.png',
    '/images/kubera-card-4.png'
  ];

  // Auto slider functionality
  useEffect(() => {
    // Track product view
    analytics.trackKuberaCardView();

    const interval = setInterval(() => {
      setCurrentImageIndex((prevIndex) => {
        const newIndex = (prevIndex + 1) % productImages.length;

        // Track image carousel interaction
        analytics.trackEvent('product_image_carousel', {
          event_category: 'product_interaction',
          image_index: newIndex,
          total_images: productImages.length
        });

        return newIndex;
      });
    }, 3000); // Change image every 3 seconds

    return () => clearInterval(interval);
  }, [productImages.length, analytics]);

  const handleBuyNow = () => {
    setShowCheckout(true);

    // Track add to cart / begin checkout
    analytics.trackKuberaCardAddToCart(2500); // Assuming price is 2500 LKR
    analytics.trackCheckoutStep(1, 'begin_checkout');

    analytics.trackEvent('begin_checkout', {
      event_category: 'ecommerce',
      currency: 'LKR',
      value: 2500,
      items: [{
        item_id: 'kubera_card',
        item_name: 'Kubera Card',
        item_category: 'spiritual_products',
        price: 2500,
        quantity: 1
      }]
    });
  };

  const nextImage = () => {
    setCurrentImageIndex((prevIndex) => 
      (prevIndex + 1) % productImages.length
    );
  };

  const prevImage = () => {
    setCurrentImageIndex((prevIndex) => 
      prevIndex === 0 ? productImages.length - 1 : prevIndex - 1
    );
  };

  const goToImage = (index) => {
    setCurrentImageIndex(index);
  };

  if (showCheckout) {
    return <KuberaCheckout onClose={() => setShowCheckout(false)} />;
  }

  return (
    <div className="kubera-card-section">
      <div className="kubera-card-container dark-glass-card">
        <div className="card-glow"></div>
        <div className="card-shine"></div>

        {/* Product Header */}
        <div className="product-header">
          <h3 className="product-title">🎴 කුබේර කාඩ්පත් 🎴</h3>
          <p className="product-subtitle">
            ධනය සහ සමෘද්ධිය ආකර්ෂණය කරගැනීම සඳහා අප විසින් බලගන්වන ලද, ඔබටම වෙන් වූ මෙම විශේෂ කුබේර කාඩ්පත කුබේර දෙවියන්ගේ ආශිර්වාදය සමඟ දැන්ම වෙන්කරවා ගන්න.
          </p>
        </div>

        {/* Product Image Slider */}
        <div className="product-slider">
          <div className="slider-container">
            <button className="slider-btn prev-btn" onClick={prevImage}>
              ‹
            </button>
            
            <div className="image-container">
              <img 
                src={productImages[currentImageIndex]} 
                alt={`කුබේර කාඩ්පත් ${currentImageIndex + 1}`}
                className="product-image"
                onError={(e) => {
                  // Fallback to a placeholder if SVG doesn't load
                  e.target.src = `https://via.placeholder.com/400x300/1a1a2e/f4d03f?text=කුබේර+කාඩ්පත්+${currentImageIndex + 1}`;
                }}
              />
              
              {/* Image overlay with product info */}
              <div className="image-overlay">
                <div className="overlay-content">
                  <span className="image-counter">
                    {currentImageIndex + 1} / {productImages.length}
                  </span>
                </div>
              </div>
            </div>
            
            <button className="slider-btn next-btn" onClick={nextImage}>
              ›
            </button>
          </div>

          {/* Slider Dots */}
          <div className="slider-dots">
            {productImages.map((_, index) => (
              <button
                key={index}
                className={`dot ${index === currentImageIndex ? 'active' : ''}`}
                onClick={() => goToImage(index)}
              />
            ))}
          </div>
        </div>

        {/* Product Details */}
        <div className="product-details">
          <div className="product-features">
            <div className="feature-item">
              <span className="feature-icon">✨</span>
              <span className="feature-text">විශේෂ කුබේර මන්ත්‍ර සහිත</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🎯</span>
              <span className="feature-text">ධනය ආකර්ෂණය කරන ශක්තිය</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">🛡️</span>
              <span className="feature-text">උසස් තත්ත්වයේ කාඩ්පත්</span>
            </div>
            <div className="feature-item">
              <span className="feature-icon">📦</span>
              <span className="feature-text">ආරක්ෂිත ඇසුරුම</span>
            </div>
          </div>

          <div className="product-pricing">
            <div className="price-section">
              <span className="original-price">රු. 2,599</span>
              <span className="current-price">රු. 1,299</span>
              <span className="discount-badge">50% OFF</span>
            </div>
            <p className="price-note">
              * නොමිලේ ගෙදර ගෙන්වා දීම (Cash on Delivery)
            </p>
          </div>
        </div>

        {/* Buy Now Button */}
        <div className="buy-now-section">
          <button className="buy-now-btn" onClick={handleBuyNow}>
            <span className="btn-icon">🛒</span>
            <span className="btn-text">දැන්ම මිලදී ගන්න</span>
            <span className="btn-arrow">→</span>
          </button>
          
          <div className="payment-info">
            <div className="payment-method">
              <span className="payment-icon">💳</span>
              <span className="payment-text">Cash on Delivery</span>
            </div>
            <div className="delivery-info">
              <span className="delivery-icon">🚚</span>
              <span className="delivery-text">2-3 දිනකින් ගෙන්වා දීම</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};



export default KuberaCardSection;
