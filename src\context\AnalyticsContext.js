/**
 * Analytics Context Provider
 * Provides analytics functionality throughout the React app
 */

import React, { createContext, useContext, useEffect, useState } from 'react';
import { initGA, setUserProperties } from '../services/analytics';

const AnalyticsContext = createContext();

export const useAnalyticsContext = () => {
  const context = useContext(AnalyticsContext);
  if (!context) {
    throw new Error('useAnalyticsContext must be used within an AnalyticsProvider');
  }
  return context;
};

export const AnalyticsProvider = ({ children }) => {
  const [isInitialized, setIsInitialized] = useState(false);
  const [userId, setUserId] = useState(null);
  const [sessionId, setSessionId] = useState(null);

  useEffect(() => {
    // Initialize Google Analytics
    const gaInitialized = initGA();
    setIsInitialized(gaInitialized);

    if (gaInitialized) {
      // Generate session ID
      const newSessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      setSessionId(newSessionId);

      // Set initial user properties
      setUserProperties({
        website_language: 'sinhala',
        website_type: 'astrology',
        content_category: 'horoscope',
        session_id: newSessionId,
        first_visit: !localStorage.getItem('kubera_returning_visitor')
      });

      // Mark as returning visitor
      localStorage.setItem('kubera_returning_visitor', 'true');
    }
  }, []);

  // Generate or retrieve user ID
  useEffect(() => {
    let storedUserId = localStorage.getItem('kubera_user_id');
    if (!storedUserId) {
      storedUserId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('kubera_user_id', storedUserId);
    }
    setUserId(storedUserId);
  }, []);

  const value = {
    isInitialized,
    userId,
    sessionId,
    
    // User preference tracking
    setUserLanguage: (language) => {
      if (isInitialized) {
        setUserProperties({ preferred_language: language });
      }
    },
    
    setUserZodiacSign: (zodiacSign) => {
      if (isInitialized) {
        setUserProperties({ user_zodiac_sign: zodiacSign });
      }
    },
    
    // Session management
    updateSessionActivity: () => {
      if (isInitialized) {
        localStorage.setItem('kubera_last_activity', Date.now().toString());
      }
    }
  };

  return (
    <AnalyticsContext.Provider value={value}>
      {children}
    </AnalyticsContext.Provider>
  );
};
