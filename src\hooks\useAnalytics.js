/**
 * React Hook for Google Analytics Integration
 * Provides easy-to-use analytics functions for React components
 */

import { useEffect, useCallback, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import {
  initGA,
  trackPageView,
  trackEvent,
  trackZodiacInteraction,
  trackKuberaCardEvent,
  trackScrollDepth,
  trackEngagement,
  trackFormSubmission,
  trackError,
  setUserProperties
} from '../services/analytics';

/**
 * Main analytics hook
 * @param {Object} options - Configuration options
 * @returns {Object} Analytics functions
 */
export const useAnalytics = (options = {}) => {
  const location = useLocation();
  const startTimeRef = useRef(Date.now());
  const scrollTrackedRef = useRef(new Set());

  // Initialize GA on mount
  useEffect(() => {
    initGA();
  }, []);

  // Track page views on route changes
  useEffect(() => {
    const path = location.pathname;
    const search = location.search;
    const fullPath = path + search;
    
    // Reset engagement tracking for new page
    startTimeRef.current = Date.now();
    scrollTrackedRef.current.clear();

    // Determine page title based on path
    let pageTitle = 'කුබේර දෙවියන්ගේ ආශීර්වාදය';
    
    if (path === '/') {
      pageTitle = 'කුබේර දෙවියන්ගේ ආශීර්වාදය - මුල් පිටුව';
    } else if (path.includes('/')) {
      // Extract zodiac sign from path
      const zodiacSign = path.replace('/', '');
      const zodiacNames = {
        'aries': 'මේෂ',
        'taurus': 'වෘෂභ',
        'gemini': 'මිථුන',
        'cancer': 'කටක',
        'leo': 'සිංහ',
        'virgo': 'කන්‍යා',
        'libra': 'තුලා',
        'scorpio': 'වෘශ්චික',
        'sagittarius': 'ධනු',
        'capricorn': 'මකර',
        'aquarius': 'කුම්භ',
        'pisces': 'මීන'
      };
      
      if (zodiacNames[zodiacSign]) {
        pageTitle = `${zodiacNames[zodiacSign]} රාශිඵල - කුබේර දෙවියන්ගේ ආශීර්වාදය`;
      }
    }

    // Track the page view
    trackPageView(fullPath, pageTitle, {
      referrer: document.referrer,
      user_language: 'si'
    });

  }, [location]);

  // Track scroll depth
  const handleScroll = useCallback(() => {
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const documentHeight = document.documentElement.scrollHeight - window.innerHeight;
    const scrollPercentage = Math.round((scrollTop / documentHeight) * 100);

    // Track scroll milestones
    const milestones = [25, 50, 75, 90, 100];
    milestones.forEach(milestone => {
      if (scrollPercentage >= milestone && !scrollTrackedRef.current.has(milestone)) {
        scrollTrackedRef.current.add(milestone);
        trackScrollDepth(milestone, location.pathname);
      }
    });
  }, [location.pathname]);

  // Set up scroll tracking
  useEffect(() => {
    window.addEventListener('scroll', handleScroll, { passive: true });
    return () => window.removeEventListener('scroll', handleScroll);
  }, [handleScroll]);

  // Track engagement time on page unload
  useEffect(() => {
    const handleBeforeUnload = () => {
      const engagementTime = (Date.now() - startTimeRef.current) / 1000;
      if (engagementTime > 5) { // Only track if user spent more than 5 seconds
        trackEngagement(engagementTime, location.pathname);
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [location.pathname]);

  // Return analytics functions
  return {
    // Page tracking
    trackPage: (path, title, params) => trackPageView(path, title, params),
    
    // Event tracking
    trackEvent: (eventName, params) => trackEvent(eventName, params),
    
    // Zodiac-specific tracking
    trackZodiacView: (zodiacSign) => trackZodiacInteraction(zodiacSign, 'view'),
    trackZodiacClick: (zodiacSign, element) => trackZodiacInteraction(zodiacSign, 'click', { element }),
    
    // Kubera Card tracking
    trackKuberaCardView: () => trackKuberaCardEvent('view_item'),
    trackKuberaCardAddToCart: (price) => trackKuberaCardEvent('add_to_cart', { price }),
    trackKuberaCardPurchase: (transactionData) => trackKuberaCardEvent('purchase', transactionData),
    
    // Form tracking
    trackFormStart: (formName) => trackEvent('form_start', { form_name: formName }),
    trackFormSubmit: (formName, data) => trackFormSubmission(formName, data),
    trackFormError: (formName, error) => trackError(`Form error in ${formName}: ${error}`, formName),
    
    // User interaction tracking
    trackButtonClick: (buttonName, location) => trackEvent('click', {
      event_category: 'button',
      event_label: buttonName,
      button_location: location
    }),
    
    trackLinkClick: (linkText, destination) => trackEvent('click', {
      event_category: 'link',
      event_label: linkText,
      link_destination: destination
    }),
    
    // Media tracking
    trackVideoPlay: (videoName) => trackEvent('video_play', {
      event_category: 'media',
      event_label: videoName
    }),
    
    trackAudioPlay: (audioName) => trackEvent('audio_play', {
      event_category: 'media',
      event_label: audioName
    }),
    
    // Error tracking
    trackError: (error, location, fatal = false) => trackError(error, location, fatal),
    
    // User properties
    setUserProperty: (properties) => setUserProperties(properties),
    
    // Custom events for this website
    trackMantaRecitation: (duration) => trackEvent('manta_recitation', {
      event_category: 'spiritual_activity',
      value: Math.round(duration),
      duration_seconds: duration
    }),
    
    trackBlessingRequest: (type) => trackEvent('blessing_request', {
      event_category: 'spiritual_activity',
      blessing_type: type
    }),
    
    trackLanguageSwitch: (fromLang, toLang) => trackEvent('language_switch', {
      event_category: 'user_preference',
      from_language: fromLang,
      to_language: toLang
    }),
    
    // Ecommerce helpers
    trackCheckoutStep: (step, option = '') => trackEvent('checkout_progress', {
      checkout_step: step,
      checkout_option: option
    }),
    
    trackPaymentMethod: (method) => trackEvent('payment_method_selected', {
      event_category: 'ecommerce',
      payment_method: method
    })
  };
};

/**
 * Hook for tracking component mount/unmount
 * @param {string} componentName - Name of the component
 */
export const useComponentTracking = (componentName) => {
  useEffect(() => {
    trackEvent('component_mount', {
      event_category: 'component_lifecycle',
      component_name: componentName
    });

    return () => {
      trackEvent('component_unmount', {
        event_category: 'component_lifecycle',
        component_name: componentName
      });
    };
  }, [componentName]);
};

/**
 * Hook for tracking form interactions
 * @param {string} formName - Name of the form
 */
export const useFormTracking = (formName) => {
  const trackFieldFocus = useCallback((fieldName) => {
    trackEvent('form_field_focus', {
      event_category: 'form_interaction',
      form_name: formName,
      field_name: fieldName
    });
  }, [formName]);

  const trackFieldBlur = useCallback((fieldName, hasValue) => {
    trackEvent('form_field_blur', {
      event_category: 'form_interaction',
      form_name: formName,
      field_name: fieldName,
      has_value: hasValue
    });
  }, [formName]);

  const trackValidationError = useCallback((fieldName, errorMessage) => {
    trackEvent('form_validation_error', {
      event_category: 'form_interaction',
      form_name: formName,
      field_name: fieldName,
      error_message: errorMessage
    });
  }, [formName]);

  return {
    trackFieldFocus,
    trackFieldBlur,
    trackValidationError
  };
};

export default useAnalytics;
