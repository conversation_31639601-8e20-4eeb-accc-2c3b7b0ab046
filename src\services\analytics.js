/**
 * Google Analytics 4 Service for Kubera Horoscope Website
 * Comprehensive tracking for page views, events, ecommerce, and user interactions
 */

// Check if Google Analytics is loaded and available
const isGALoaded = () => {
  return typeof window !== 'undefined' && typeof window.gtag === 'function';
};

// Get the GA Measurement ID from environment variables
const GA_MEASUREMENT_ID = process.env.REACT_APP_GA_MEASUREMENT_ID;

/**
 * Initialize Google Analytics
 */
export const initGA = () => {
  if (!GA_MEASUREMENT_ID || GA_MEASUREMENT_ID === 'G-XXXXXXXXXX') {
    console.warn('Google Analytics Measurement ID not configured. Please set REACT_APP_GA_MEASUREMENT_ID in your .env file.');
    return false;
  }

  if (isGALoaded()) {
    console.log('Google Analytics initialized with ID:', GA_MEASUREMENT_ID);
    return true;
  }
  
  console.warn('Google Analytics not loaded. Make sure gtag script is included in index.html');
  return false;
};

/**
 * Track page views
 * @param {string} path - The page path
 * @param {string} title - The page title
 * @param {Object} additionalParams - Additional parameters
 */
export const trackPageView = (path, title = '', additionalParams = {}) => {
  if (!isGALoaded()) return;

  const params = {
    page_title: title,
    page_location: `${window.location.origin}${path}`,
    page_path: path,
    ...additionalParams
  };

  window.gtag('config', GA_MEASUREMENT_ID, params);
  
  // Also send as a page_view event for better tracking
  window.gtag('event', 'page_view', {
    page_title: title,
    page_location: `${window.location.origin}${path}`,
    page_path: path,
    ...additionalParams
  });

  console.log('Page view tracked:', path, title);
};

/**
 * Track custom events
 * @param {string} eventName - Name of the event
 * @param {Object} parameters - Event parameters
 */
export const trackEvent = (eventName, parameters = {}) => {
  if (!isGALoaded()) return;

  window.gtag('event', eventName, {
    event_category: parameters.category || 'engagement',
    event_label: parameters.label || '',
    value: parameters.value || 0,
    ...parameters
  });

  console.log('Event tracked:', eventName, parameters);
};

/**
 * Track zodiac sign interactions
 * @param {string} zodiacSign - The zodiac sign (e.g., 'aries', 'taurus')
 * @param {string} action - The action taken (e.g., 'view', 'click')
 * @param {Object} additionalParams - Additional parameters
 */
export const trackZodiacInteraction = (zodiacSign, action = 'view', additionalParams = {}) => {
  trackEvent('zodiac_interaction', {
    category: 'zodiac',
    label: zodiacSign,
    zodiac_sign: zodiacSign,
    interaction_type: action,
    ...additionalParams
  });
};

/**
 * Track Kubera Card product interactions
 * @param {string} action - The action (view_item, add_to_cart, purchase, etc.)
 * @param {Object} productData - Product information
 */
export const trackKuberaCardEvent = (action, productData = {}) => {
  if (!isGALoaded()) return;

  const defaultProductData = {
    item_id: 'kubera_card',
    item_name: 'Kubera Card',
    item_category: 'spiritual_products',
    item_category2: 'astrology',
    price: productData.price || 0,
    currency: 'LKR',
    quantity: productData.quantity || 1
  };

  const eventData = {
    currency: 'LKR',
    value: productData.price || 0,
    items: [{ ...defaultProductData, ...productData }]
  };

  window.gtag('event', action, eventData);
  console.log('Kubera Card event tracked:', action, eventData);
};

/**
 * Track ecommerce purchase
 * @param {Object} purchaseData - Purchase information
 */
export const trackPurchase = (purchaseData) => {
  if (!isGALoaded()) return;

  const {
    transaction_id,
    value,
    currency = 'LKR',
    items = [],
    shipping = 0,
    tax = 0
  } = purchaseData;

  window.gtag('event', 'purchase', {
    transaction_id,
    value,
    currency,
    items,
    shipping,
    tax
  });

  console.log('Purchase tracked:', purchaseData);
};

/**
 * Track form submissions
 * @param {string} formName - Name of the form
 * @param {Object} formData - Form data (be careful with PII)
 */
export const trackFormSubmission = (formName, formData = {}) => {
  trackEvent('form_submit', {
    category: 'form',
    label: formName,
    form_name: formName,
    ...formData
  });
};

/**
 * Track user engagement time
 * @param {number} engagementTime - Time in seconds
 * @param {string} page - Page identifier
 */
export const trackEngagement = (engagementTime, page = '') => {
  trackEvent('user_engagement', {
    category: 'engagement',
    label: page,
    value: Math.round(engagementTime),
    engagement_time_msec: engagementTime * 1000
  });
};

/**
 * Track scroll depth
 * @param {number} scrollPercentage - Percentage scrolled (0-100)
 * @param {string} page - Page identifier
 */
export const trackScrollDepth = (scrollPercentage, page = '') => {
  // Only track at certain milestones
  const milestones = [25, 50, 75, 90, 100];
  if (milestones.includes(scrollPercentage)) {
    trackEvent('scroll', {
      category: 'engagement',
      label: `${scrollPercentage}%`,
      value: scrollPercentage,
      page_path: page
    });
  }
};

/**
 * Track search events
 * @param {string} searchTerm - What the user searched for
 * @param {number} resultsCount - Number of results returned
 */
export const trackSearch = (searchTerm, resultsCount = 0) => {
  trackEvent('search', {
    category: 'search',
    label: searchTerm,
    search_term: searchTerm,
    results_count: resultsCount
  });
};

/**
 * Track language preference
 * @param {string} language - Language code (e.g., 'si', 'en')
 */
export const trackLanguagePreference = (language) => {
  trackEvent('language_preference', {
    category: 'user_preference',
    label: language,
    language: language
  });
};

/**
 * Track errors
 * @param {string} errorMessage - Error message
 * @param {string} errorLocation - Where the error occurred
 * @param {boolean} fatal - Whether the error was fatal
 */
export const trackError = (errorMessage, errorLocation = '', fatal = false) => {
  trackEvent('exception', {
    description: errorMessage,
    fatal: fatal,
    error_location: errorLocation
  });
};

/**
 * Track timing events (e.g., page load time, API response time)
 * @param {string} name - Name of the timing event
 * @param {number} value - Time in milliseconds
 * @param {string} category - Category of the timing event
 */
export const trackTiming = (name, value, category = 'performance') => {
  trackEvent('timing_complete', {
    name: name,
    value: Math.round(value),
    event_category: category
  });
};

/**
 * Set user properties
 * @param {Object} properties - User properties to set
 */
export const setUserProperties = (properties) => {
  if (!isGALoaded()) return;

  window.gtag('config', GA_MEASUREMENT_ID, {
    user_properties: properties
  });

  console.log('User properties set:', properties);
};

/**
 * Track outbound links
 * @param {string} url - The external URL
 * @param {string} linkText - Text of the link
 */
export const trackOutboundLink = (url, linkText = '') => {
  trackEvent('click', {
    event_category: 'outbound',
    event_label: url,
    link_text: linkText,
    link_url: url
  });
};

// Export the GA Measurement ID for use in other components
export { GA_MEASUREMENT_ID };
